---
title: 2FA
---

The user can enable 2FA from the Account page. The Account page is accessible from the dropdown menu in the top right corner of the application.

## Enable TOTP

- **Enable Two Factor Authentication**: Enable TOTP for the user.

## Configure TOTP

A QR code will be displayed. Scan the QR code with your TOTP application (e.g., Google Authenticator, Authy, etc.) to add the account.

To verify that the TOTP key is working, the user must enter the TOTP code generated by the TOTP application.

- **TOTP Key**: The TOTP key for the user. This key is used to generate the TOTP code.
- **TOTP Code**: The current TOTP code for the user. This code is used to verify the TOTP key.
- **Enable Two Factor Authentication**: Enable TOTP for the user.

## Disable TOTP

To disable TOTP, the user must enter the current password.

- **Current Password**: The current password of the user.
- **Disable Two Factor Authentication**: Disable TOTP for the user.
