---
title: Home
hide:
    - navigation
---

# Welcome to the Documentation for `wg-easy`

/// info | This Documentation is Versioned

**Make sure** to select the correct version of this documentation! It should match the version of the image you are using. The default version corresponds to the `:latest` image tag - [the most recent stable release][docs-tagging].
///

This documentation provides you not only with the basic setup and configuration of `wg-easy` but also with advanced configuration, elaborate usage scenarios, detailed examples, hints and more.

[docs-tagging]: ./getting-started.md#tagging-convention

## About

`wg-easy` is the easiest way to run WireGuard VPN + Web-based Admin UI.

## Contents

### Getting Started

If you're new to wg-easy, make sure to read the [_Getting Started_ chapter][docs-getting-started] first. If you want to look at examples for Docker Run and Compose, we have an [_Examples_ page][docs-examples].

[docs-getting-started]: ./getting-started.md
[docs-examples]: ./examples/tutorials/basic-installation.md

### Contributing

We are always happy to welcome new contributors. For guidelines and entrypoints please have a look at the [Contributing section][docs-contributing].

[docs-contributing]: ./contributing/issues-and-pull-requests.md

### Migration

If you are migrating from an older version of `wg-easy`, please read the [_Migration_ chapter][docs-migration].

[docs-migration]: ./advanced/migrate/from-14-to-15.md
