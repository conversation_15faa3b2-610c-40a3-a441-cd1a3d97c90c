site_name: 'wg-easy'
site_description: 'The easiest way to run WireGuard VPN + Web-based Admin UI.'
site_author: 'WireGuard Easy'
copyright: >
    <p>
    &copy <a href="https://github.com/wg-easy"><em>Wireguard Easy</em></a><br/>
    <span>This project is licensed under AGPL-3.0-only.</span><br/>
    <span>This project is not affiliated, associated, authorized, endorsed by, or in any way officially connected with <PERSON>, ZX2C4 or Edge Security</span><br/>
    <span>"WireGuard" and the "WireGuard" logo are registered trademarks of <PERSON></span>
    </p>

repo_url: https://github.com/wg-easy/wg-easy
repo_name: wg-easy

edit_uri: 'edit/master/docs/content'

docs_dir: 'content/'

site_url: https://wg-easy.github.io/wg-easy

theme:
    name: material
    favicon: assets/logo/favicon.png
    logo: assets/logo/logo.png
    icon:
        repo: fontawesome/brands/github
    features:
        - navigation.tabs
        - navigation.top
        - navigation.expand
        - navigation.instant
        - content.action.edit
        - content.action.view
        - content.code.annotate
    palette:
        # Light mode
        - media: '(prefers-color-scheme: light)'
          scheme: default
          primary: grey
          accent: red
          toggle:
              icon: material/weather-night
              name: Switch to dark mode
        # Dark mode
        - media: '(prefers-color-scheme: dark)'
          scheme: slate
          primary: grey
          accent: red
          toggle:
              icon: material/weather-sunny
              name: Switch to light mode

extra:
    version:
        provider: mike

markdown_extensions:
    - toc:
          anchorlink: true
    - abbr
    - attr_list
    - pymdownx.blocks.admonition:
          types:
              - danger
              - note
              - info
              - question
              - warning
    - pymdownx.details
    - pymdownx.superfences:
          custom_fences:
              - name: mermaid
                class: mermaid
                format: !!python/name:pymdownx.superfences.fence_code_format
    - pymdownx.tabbed:
          alternate_style: true
          slugify: !!python/object/apply:pymdownx.slugs.slugify
              kwds:
                  case: lower
    - pymdownx.tasklist:
          custom_checkbox: true
    - pymdownx.magiclink
    - pymdownx.inlinehilite
    - pymdownx.tilde
    - pymdownx.emoji:
          emoji_index: !!python/name:material.extensions.emoji.twemoji
          emoji_generator: !!python/name:material.extensions.emoji.to_svg
