volumes:
  etc_wireguard:

services:
  wg-easy:
    environment:
    #  Optional:
    #  - PORT=51821
    #  - HOST=0.0.0.0
     - INIT_ENABLED=true
     - INIT_HOST=*********
     - INIT_PORT=51820
     - INIT_USERNAME=testtest
     - INIT_PASSWORD=Qweasdyxcv!2
     - DISABLE_IPV6=true
     - INSECURE=true

    image: ztbcs/wg:latest
    container_name: wg-easy
    networks:
      wg:
        ipv4_address: ***********
        ipv6_address: fdcc:ad94:bacf:61a3::2a
    volumes:
      - etc_wireguard:/etc/wireguard
      - /lib/modules:/lib/modules:ro
    ports:
      - "51820:51820/udp"
      - "51821:51821/tcp"
    restart: unless-stopped
    cap_add:
      - NET_ADMIN
      - SYS_MODULE
      # - NET_RAW # ⚠️ Uncomment if using Podman
    sysctls:
      - net.ipv4.ip_forward=1
      - net.ipv4.conf.all.src_valid_mark=1
      - net.ipv6.conf.all.disable_ipv6=0
      - net.ipv6.conf.all.forwarding=1
      - net.ipv6.conf.default.forwarding=1

networks:
  wg:
    driver: bridge
    enable_ipv6: true
    ipam:
      driver: default
      config:
        - subnet: **********/24
        - subnet: fdcc:ad94:bacf:61a3::/64
