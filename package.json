{"version": "1.0.0", "private": true, "scripts": {"dev": "docker compose -f docker-compose.dev.yml up wg-easy --build", "cli:dev": "docker compose -f docker-compose.dev.yml run --build --rm -it wg-easy cli:dev", "build": "docker build --network=host -t ztbcs/wg:latest .", "docs:preview": "docker run --rm -it -p 8080:8080 -v ./docs:/docs squidfunk/mkdocs-material serve -a 0.0.0.0:8080", "scripts:version": "bash scripts/version.sh", "format:check:docs": "prettier --check docs"}, "devDependencies": {"prettier": "^3.6.2"}, "packageManager": "pnpm@10.12.4", "dependencies": {"net-ping": "^1.2.4"}}