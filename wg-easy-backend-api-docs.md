# WG-Easy 后端 API 文档

## 项目概述

WG-Easy 是一个基于 Nuxt.js 的 WireGuard VPN 管理系统，提供了完整的 REST API 用于管理 WireGuard 客户端、服务器配置和用户权限。

## 服务器初始化

### 环境变量配置

```bash
# 必需环境变量
PORT=51821                    # UI 监听端口

# 可选环境变量
INSECURE=false               # 是否使用 HTTP 而非 HTTPS
DISABLE_IPV6=false           # 是否禁用 IPv6

# 初始化配置（可选）
INIT_ENABLED=true            # 启用自动初始化
INIT_USERNAME=admin          # 初始管理员用户名
INIT_PASSWORD=password       # 初始管理员密码
INIT_DNS=*******,*******    # 默认 DNS 服务器
INIT_IPV4_CIDR=********/24   # IPv4 CIDR
INIT_IPV6_CIDR=fd42:42:42::0/64  # IPv6 CIDR
INIT_HOST=your-server.com    # 服务器主机名
INIT_PORT=51820              # WireGuard 端口
```

### 启动流程

1. **数据库初始化**: SQLite 数据库位于 `/etc/wireguard/wg-easy.db`
2. **自动迁移**: 启动时自动执行数据库迁移
3. **初始设置**: 如果启用 `INIT_ENABLED`，自动创建管理员用户和基础配置
4. **WireGuard 启动**: 初始化 WireGuard 接口和配置

### Docker 启动

```bash
# 开发环境
docker compose -f docker-compose.dev.yml up wg-easy --build

# 生产环境
docker build --network=host -t ztbcs/wg:latest .
```

## 认证机制

### 会话认证
- 基于 Cookie 的会话管理
- 支持"记住我"功能
- 会话超时可配置

### TOTP 二次验证
- 支持 TOTP (Time-based One-Time Password)
- 使用 SHA1 算法，6位数字，30秒周期

### 权限系统
- 基于角色的权限控制 (RBAC)
- 支持资源级权限检查

## API 端点

### 1. 认证相关

#### 登录
```http
POST /api/session
Content-Type: application/json

{
  "username": "admin",
  "password": "password",
  "remember": true,
  "totpCode": "123456"  // 可选，启用TOTP时必需
}
```

**响应示例:**
```json
{
  "status": "success"
}
```

**错误响应:**
```json
{
  "status": "TOTP_REQUIRED"
}
// 或
{
  "status": "INVALID_TOTP_CODE"
}
```

#### 获取当前用户
```http
GET /api/session
```

**响应示例:**
```json
{
  "id": 1,
  "role": "admin",
  "username": "admin",
  "name": "Administrator",
  "email": "<EMAIL>",
  "totpVerified": true
}
```

#### 登出
```http
DELETE /api/session
```

**响应示例:**
```json
{
  "success": true
}
```

### 2. 用户管理

#### 更新用户信息
```http
POST /api/me
Content-Type: application/json

{
  "name": "New Name",
  "email": "<EMAIL>"
}
```

#### 修改密码
```http
POST /api/me/password
Content-Type: application/json

{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword"
}
```

#### TOTP 管理
```http
POST /api/me/totp
Content-Type: application/json

// 设置 TOTP
{
  "type": "setup"
}

// 验证并启用 TOTP
{
  "type": "create",
  "code": "123456"
}

// 删除 TOTP
{
  "type": "delete",
  "currentPassword": "password"
}
```

### 3. 客户端管理

#### 获取所有客户端
```http
GET /api/client
```

**响应示例:**
```json
[
  {
    "id": 1,
    "name": "Client 1",
    "enabled": true,
    "ipv4Address": "********",
    "ipv6Address": "fd42:42:42::2",
    "publicKey": "...",
    "transferRx": 1024,
    "transferTx": 2048,
    "latestHandshakeAt": "2024-01-01T00:00:00.000Z",
    "expiresAt": null,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
]
```

#### 创建客户端
```http
POST /api/client
Content-Type: application/json

{
  "name": "New Client",
  "expiresAt": "2024-12-31T23:59:59.000Z"  // 可选
}
```

#### 获取单个客户端
```http
GET /api/client/{clientId}
```

#### 更新客户端
```http
POST /api/client/{clientId}
Content-Type: application/json

{
  "name": "Updated Client",
  "enabled": true,
  "expiresAt": null,
  "ipv4Address": "********",
  "ipv6Address": "fd42:42:42::2",
  "preUp": "",
  "postUp": "",
  "preDown": "",
  "postDown": "",
  "allowedIps": ["0.0.0.0/0", "::/0"],
  "serverAllowedIps": ["***********/24"],
  "mtu": 1420,
  "persistentKeepalive": 25,
  "serverEndpoint": "vpn.example.com:51820",
  "dns": ["*******", "*******"]
}
```

#### 删除客户端
```http
DELETE /api/client/{clientId}
```

#### 启用/禁用客户端
```http
POST /api/client/{clientId}/enable
POST /api/client/{clientId}/disable
```

#### 获取客户端 QR 码
```http
GET /api/client/{clientId}/qrcode.svg
Content-Type: image/svg+xml
```

#### 生成一次性链接
```http
POST /api/client/{clientId}/generateOneTimeLink
```

### 4. 设备管理

#### 获取客户端设备列表
```http
GET /api/client/{clientId}/equipment
```

**响应示例:**
```json
[
  {
    "id": 1,
    "clientId": 1,
    "ipAddress": "*************",
    "name": "Server",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
]
```

#### 设备连通性测试
```http
GET /api/client/{clientId}/equipment/ping
```

**响应示例:**
```json
[
  {
    "id": 1,
    "status": "Online",
    "latency": 25
  },
  {
    "id": 2,
    "status": "Offline",
    "latency": null
  }
]
```

### 5. 管理员功能

#### 获取系统信息
```http
GET /api/information
```

**响应示例:**
```json
{
  "currentRelease": "v15.1.0",
  "latestRelease": {
    "version": "v15.2.0",
    "url": "https://github.com/wg-easy/wg-easy/releases/tag/v15.2.0"
  },
  "updateAvailable": true,
  "insecure": false
}
```

#### 获取通用配置
```http
GET /api/admin/general
```

**响应示例:**
```json
{
  "sessionTimeout": 3600,
  "metricsPrometheus": true,
  "metricsJson": false,
  "metricsPassword": null
}
```

#### 更新通用配置
```http
POST /api/admin/general
Content-Type: application/json

{
  "sessionTimeout": 7200,
  "metricsPrometheus": true,
  "metricsJson": true,
  "metricsPassword": "metrics_password"
}
```

#### 获取接口配置
```http
GET /api/admin/interface
```

**响应示例:**
```json
{
  "name": "wg0",
  "ipv4Cidr": "********/24",
  "ipv6Cidr": "fd42:42:42::0/64",
  "port": 51820,
  "mtu": 1420,
  "device": "wg0",
  "enabled": true,
  "publicKey": "...",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

#### 更新接口配置
```http
POST /api/admin/interface
Content-Type: application/json

{
  "ipv4Cidr": "********/24",
  "ipv6Cidr": "fd42:42:42::0/64",
  "port": 51820,
  "mtu": 1420,
  "device": "wg0",
  "enabled": true
}
```

#### 更新 CIDR 配置
```http
POST /api/admin/interface/cidr
Content-Type: application/json

{
  "ipv4Cidr": "********/24",
  "ipv6Cidr": "fd43:43:43::0/64"
}
```

#### 重启接口
```http
POST /api/admin/interface/restart
```

#### 获取 Hooks 配置
```http
GET /api/admin/hooks
```

**响应示例:**
```json
{
  "preUp": "echo 'Pre-up hook'",
  "postUp": "echo 'Post-up hook'",
  "preDown": "echo 'Pre-down hook'",
  "postDown": "echo 'Post-down hook'"
}
```

#### 更新 Hooks 配置
```http
POST /api/admin/hooks
Content-Type: application/json

{
  "preUp": "iptables -A FORWARD -i wg0 -j ACCEPT",
  "postUp": "iptables -A FORWARD -o wg0 -j ACCEPT",
  "preDown": "iptables -D FORWARD -i wg0 -j ACCEPT",
  "postDown": "iptables -D FORWARD -o wg0 -j ACCEPT"
}
```

#### 获取用户配置
```http
GET /api/admin/userconfig
```

#### 更新用户配置
```http
POST /api/admin/userconfig
Content-Type: application/json

{
  "host": "vpn.example.com",
  "port": 51820
}
```

#### 获取 IP 信息
```http
GET /api/admin/ip-info
```

**响应示例:**
```json
{
  "ip": "***********",
  "country": "US",
  "city": "New York",
  "timezone": "America/New_York"
}
```

### 6. 监控指标

#### Prometheus 格式指标
```http
GET /metrics/prometheus
Authorization: Bearer <token>  # 如果设置了密码
Content-Type: text/plain
```

**响应示例:**
```
# HELP wg-easy and wireguard metrics

# HELP wireguard_configured_peers
# TYPE wireguard_configured_peers gauge
wireguard_configured_peers{interface="wg0"} 5

# HELP wireguard_enabled_peers
# TYPE wireguard_enabled_peers gauge
wireguard_enabled_peers{interface="wg0"} 3

# HELP wireguard_connected_peers
# TYPE wireguard_connected_peers gauge
wireguard_connected_peers{interface="wg0"} 2

# HELP wireguard_sent_bytes Bytes sent to the peer
# TYPE wireguard_sent_bytes counter
wireguard_sent_bytes{interface="wg0",enabled="true",ipv4Address="********",ipv6Address="fd42:42:42::2",name="Client1"} 1024

# HELP wireguard_received_bytes Bytes received from the peer
# TYPE wireguard_received_bytes counter
wireguard_received_bytes{interface="wg0",enabled="true",ipv4Address="********",ipv6Address="fd42:42:42::2",name="Client1"} 2048
```

#### JSON 格式指标
```http
GET /metrics/json
Authorization: Bearer <token>  # 如果设置了密码
Content-Type: application/json
```

**响应示例:**
```json
{
  "wireguardPeerCount": 5,
  "wireguardEnabledPeersCount": 3,
  "wireguardConnectedPeersCount": 2,
  "clients": [
    {
      "name": "Client1",
      "enabled": true,
      "connected": true,
      "transferRx": 2048,
      "transferTx": 1024,
      "ipv4Address": "********",
      "ipv6Address": "fd42:42:42::2"
    }
  ]
}
```

### 7. 初始设置

#### 创建管理员用户 (设置步骤 2)
```http
POST /api/setup/2
Content-Type: application/json

{
  "username": "admin",
  "password": "secure_password"
}
```

#### 获取服务器信息 (设置步骤 4)
```http
GET /api/setup/4
```

#### 配置服务器 (设置步骤 4)
```http
POST /api/setup/4
Content-Type: application/json

{
  "host": "vpn.example.com",
  "port": 51820
}
```

#### 迁移配置
```http
POST /api/setup/migrate
Content-Type: application/json

{
  "file": "{\"server\":{\"privateKey\":\"...\",\"publicKey\":\"...\",\"address\":\"********\"},\"clients\":{...}}"
}
```

## 错误处理

### HTTP 状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证或认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "statusCode": 400,
  "statusMessage": "Invalid request parameters"
}
```

## 数据验证

### 通用验证规则
- **端口**: 1-65535
- **MTU**: 1280-9000
- **CIDR**: 有效的 CIDR 格式
- **IP 地址**: 有效的 IPv4/IPv6 地址
- **字符串**: 不能包含 `__proto__`, `constructor`, `prototype`

### 客户端验证
- **名称**: 必需，非空字符串
- **过期时间**: 可选，ISO 8601 格式
- **允许的 IP**: IP 地址数组
- **持久连接**: 0-65535 秒

## 权限系统

### 角色类型
- `admin`: 管理员，拥有所有权限
- `user`: 普通用户，有限权限

### 资源权限
- `clients`: 客户端管理 (view, create, update, delete)
- `admin`: 管理员功能 (any)
- `me`: 个人信息管理 (update)

## 技术栈

- **框架**: Nuxt.js 4.x
- **运行时**: Node.js
- **数据库**: SQLite (Drizzle ORM)
- **验证**: Zod
- **认证**: 基于 Cookie 的会话
- **容器**: Docker

## 部署说明

1. **环境准备**: 确保 Docker 和 Docker Compose 已安装
2. **配置环境变量**: 根据需要设置环境变量
3. **启动服务**: 使用 Docker Compose 启动
4. **初始设置**: 访问 Web 界面完成初始设置
5. **防火墙配置**: 开放必要的端口 (默认 51821 for UI, 51820 for WireGuard)

## 安全建议

1. **使用 HTTPS**: 生产环境中启用 HTTPS
2. **强密码**: 使用强密码和 TOTP 二次验证
3. **定期备份**: 定期备份数据库和配置
4. **监控日志**: 监控访问日志和错误日志
5. **网络隔离**: 适当配置防火墙规则
