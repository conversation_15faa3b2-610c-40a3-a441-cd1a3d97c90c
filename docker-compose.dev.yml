services:
  wg-easy:
    build:
      dockerfile: ./Dockerfile.dev
    command: dev
    volumes:
      - ./src/:/app/
      - temp:/app/.nuxt/
      - temp1:/app/node_modules/
      - /lib/modules:/lib/modules:ro
      - ./data/:/etc/wireguard
    ports:
      - "51820:51820/udp"
      - "51821:51821/tcp"
    cap_add:
      - NET_ADMIN
      - SYS_MODULE
    environment:
      - INIT_ENABLED=true
      - INIT_HOST=test
      - INIT_PORT=51820
      - INIT_USERNAME=testtest
      - INIT_PASSWORD=Qweasdyxcv!2
      - DISABLE_IPV6=true
      - INSECURE=true

# folders should be generated inside container
volumes:
  temp:
    driver: local
  temp1:
    driver: local
