CREATE TABLE `equipment_table` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`client_id` integer NOT NULL,
	`ip_address` text NOT NULL,
	`name` text DEFAULT '' NOT NULL,
	`description` text DEFAULT '' NOT NULL,
	`enabled` integer DEFAULT 1 NOT NULL,
	`created_at` text DEFAULT (CURRENT_TIMESTAMP) NOT NULL,
	`updated_at` text DEFAULT (CURRENT_TIMESTAMP) NOT NULL,
	FOREIGN KEY (`client_id`) REFERENCES `clients_table`(`id`) ON UPDATE cascade ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `equipment_client_ip_unique` ON `equipment_table` (`client_id`, `ip_address`);
--> statement-breakpoint

-- Migrate existing serverAllowedIps data to equipment table
INSERT INTO `equipment_table` (`client_id`, `ip_address`, `name`, `description`, `enabled`)
SELECT 
    c.id as client_id,
    CASE 
        WHEN json_extract(value, '$') LIKE '%/%' THEN json_extract(value, '$')
        ELSE json_extract(value, '$') || '/32'
    END as ip_address,
    '' as name,
    '' as description,
    1 as enabled
FROM `clients_table` c,
     json_each(c.server_allowed_ips)
WHERE c.server_allowed_ips IS NOT NULL 
  AND c.server_allowed_ips != '[]'
  AND json_extract(value, '$') IS NOT NULL
  AND json_extract(value, '$') != '';
