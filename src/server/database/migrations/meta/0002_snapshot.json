{"version": "6", "dialect": "sqlite", "id": "fea6b02b-0bb1-47ce-8340-718e20b8bad3", "prevId": "78de2e52-c4a8-4900-86c5-92f34739623a", "tables": {"clients_table": {"name": "clients_table", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "interface_id": {"name": "interface_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ipv4_address": {"name": "ipv4_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ipv6_address": {"name": "ipv6_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "pre_up": {"name": "pre_up", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "post_up": {"name": "post_up", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "pre_down": {"name": "pre_down", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "post_down": {"name": "post_down", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "private_key": {"name": "private_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "public_key": {"name": "public_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "pre_shared_key": {"name": "pre_shared_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "allowed_ips": {"name": "allowed_ips", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "server_allowed_ips": {"name": "server_allowed_ips", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "persistent_keepalive": {"name": "persistent_keepalive", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "mtu": {"name": "mtu", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "dns": {"name": "dns", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "server_endpoint": {"name": "server_endpoint", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "enabled": {"name": "enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}}, "indexes": {"clients_table_ipv4_address_unique": {"name": "clients_table_ipv4_address_unique", "columns": ["ipv4_address"], "isUnique": true}, "clients_table_ipv6_address_unique": {"name": "clients_table_ipv6_address_unique", "columns": ["ipv6_address"], "isUnique": true}}, "foreignKeys": {"clients_table_user_id_users_table_id_fk": {"name": "clients_table_user_id_users_table_id_fk", "tableFrom": "clients_table", "tableTo": "users_table", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "clients_table_interface_id_interfaces_table_name_fk": {"name": "clients_table_interface_id_interfaces_table_name_fk", "tableFrom": "clients_table", "tableTo": "interfaces_table", "columnsFrom": ["interface_id"], "columnsTo": ["name"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "general_table": {"name": "general_table", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false, "default": 1}, "setup_step": {"name": "setup_step", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "session_password": {"name": "session_password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "session_timeout": {"name": "session_timeout", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "metrics_prometheus": {"name": "metrics_prometheus", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "metrics_json": {"name": "metrics_json", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "metrics_password": {"name": "metrics_password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "hooks_table": {"name": "hooks_table", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "pre_up": {"name": "pre_up", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_up": {"name": "post_up", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "pre_down": {"name": "pre_down", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "post_down": {"name": "post_down", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}}, "indexes": {}, "foreignKeys": {"hooks_table_id_interfaces_table_name_fk": {"name": "hooks_table_id_interfaces_table_name_fk", "tableFrom": "hooks_table", "tableTo": "interfaces_table", "columnsFrom": ["id"], "columnsTo": ["name"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "interfaces_table": {"name": "interfaces_table", "columns": {"name": {"name": "name", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "device": {"name": "device", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "port": {"name": "port", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "private_key": {"name": "private_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "public_key": {"name": "public_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ipv4_cidr": {"name": "ipv4_cidr", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ipv6_cidr": {"name": "ipv6_cidr", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "mtu": {"name": "mtu", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "enabled": {"name": "enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}}, "indexes": {"interfaces_table_port_unique": {"name": "interfaces_table_port_unique", "columns": ["port"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "one_time_links_table": {"name": "one_time_links_table", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "one_time_link": {"name": "one_time_link", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}}, "indexes": {"one_time_links_table_one_time_link_unique": {"name": "one_time_links_table_one_time_link_unique", "columns": ["one_time_link"], "isUnique": true}}, "foreignKeys": {"one_time_links_table_id_clients_table_id_fk": {"name": "one_time_links_table_id_clients_table_id_fk", "tableFrom": "one_time_links_table", "tableTo": "clients_table", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users_table": {"name": "users_table", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_plaintext": {"name": "password_plaintext", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "totp_key": {"name": "totp_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "totp_verified": {"name": "totp_verified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "enabled": {"name": "enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}}, "indexes": {"users_table_username_unique": {"name": "users_table_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_configs_table": {"name": "user_configs_table", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "default_mtu": {"name": "default_mtu", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "default_persistent_keepalive": {"name": "default_persistent_keepalive", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "default_dns": {"name": "default_dns", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "default_allowed_ips": {"name": "default_allowed_ips", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "host": {"name": "host", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "port": {"name": "port", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}}, "indexes": {}, "foreignKeys": {"user_configs_table_id_interfaces_table_name_fk": {"name": "user_configs_table_id_interfaces_table_name_fk", "tableFrom": "user_configs_table", "tableTo": "interfaces_table", "columnsFrom": ["id"], "columnsTo": ["name"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}