import { sql, relations } from 'drizzle-orm';
import { int, sqliteTable, text } from 'drizzle-orm/sqlite-core';

import { client } from '../../schema';

export const user = sqliteTable('users_table', {
  id: int().primaryKey({ autoIncrement: true }),
  username: text().notNull().unique(),
  password: text().notNull(),
  passwordPlaintext: text('password_plaintext'), // Hidden field for backend operations only
  email: text(),
  name: text().notNull(),
  role: int().$type<Role>().notNull(),
  totpKey: text('totp_key'),
  totpVerified: int('totp_verified', { mode: 'boolean' }).notNull(),
  enabled: int({ mode: 'boolean' }).notNull(),
  createdAt: text('created_at')
    .notNull()
    .default(sql`(CURRENT_TIMESTAMP)`),
  updatedAt: text('updated_at')
    .notNull()
    .default(sql`(CURRENT_TIMESTAMP)`)
    .$onUpdate(() => sql`(CURRENT_TIMESTAMP)`),
});

export const usersRelations = relations(user, ({ many }) => ({
  clients: many(client),
}));
