import type { InferSelectModel } from 'drizzle-orm';
import z from 'zod';

import type { equipment } from './schema';
import {
  t,
  safeStringRefine,
  EnabledSchema,
  schemaForType
} from '#utils/types';

export type EquipmentType = InferSelectModel<typeof equipment>;

export type CreateEquipmentType = Omit<
  EquipmentType,
  'createdAt' | 'updatedAt' | 'id'
>;

export type UpdateEquipmentType = Partial<Omit<
  CreateEquipmentType,
  'clientId'
>>;

const ipAddress = z
  .string({ message: t('zod.equipment.ipAddress') })
  .min(1, t('zod.equipment.ipAddress'))
  .pipe(safeStringRefine);

const name = z
  .string({ message: t('zod.equipment.name') })
  .pipe(safeStringRefine);

const description = z
  .string({ message: t('zod.equipment.description') })
  .pipe(safeStringRefine);

export const EquipmentCreateSchema = z.object({
  ipAddress: ipAddress,
  name: name.default(''),
  description: description.default(''),
});

export type EquipmentCreateType = z.infer<typeof EquipmentCreateSchema>;

export const EquipmentUpdateSchema = schemaForType<UpdateEquipmentType>()(
  z.object({
    ipAddress: ipAddress.optional(),
    name: name.optional(),
    description: description.optional(),
    enabled: EnabledSchema.optional(),
  })
);

// TODO: investigate if coerce is bad
const equipmentId = z.number({ message: t('zod.equipment.id'), coerce: true });

export const EquipmentGetSchema = z.object({
  equipmentId: equipmentId,
});

export const ClientEquipmentGetSchema = z.object({
  clientId: z.number({ message: t('zod.client.id'), coerce: true }),
});
