import { sql, relations } from 'drizzle-orm';
import { int, sqliteTable, text } from 'drizzle-orm/sqlite-core';

import { client } from '../client/schema';

export const equipment = sqliteTable('equipment_table', {
  id: int().primaryKey({ autoIncrement: true }),
  clientId: int('client_id')
    .notNull()
    .references(() => client.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  ipAddress: text('ip_address').notNull(),
  name: text().default('').notNull(),
  description: text().default('').notNull(),
  enabled: int({ mode: 'boolean' }).notNull().default(true),
  createdAt: text('created_at')
    .notNull()
    .default(sql`(CURRENT_TIMESTAMP)`),
  updatedAt: text('updated_at')
    .notNull()
    .default(sql`(CURRENT_TIMESTAMP)`)
    .$onUpdate(() => sql`(CURRENT_TIMESTAMP)`),
});

export const equipmentRelations = relations(equipment, ({ one }) => ({
  client: one(client, {
    fields: [equipment.clientId],
    references: [client.id],
  }),
}));
