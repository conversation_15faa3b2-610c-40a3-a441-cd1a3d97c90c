import { eq, sql, and } from 'drizzle-orm';
import { exec } from 'child_process';
import { promisify } from 'util';
import { equipment } from './schema';
import type {
  EquipmentCreateType,
  UpdateEquipmentType,
} from './types';
import type { DBType } from '#db/sqlite';

const execAsync = promisify(exec);

// Ping function to check if an IP address is reachable using system ping command
async function pingIP(ipAddress: string): Promise<boolean> {
  try {
    // Remove CIDR notation if present (e.g., *************/32 -> *************)
    const ip = ipAddress.split('/')[0];

    // Use ping command with timeout of 2 seconds and 1 packet
    // -c 1: send only 1 packet
    // -W 2: timeout after 2 seconds
    const { stdout } = await execAsync(`ping -c 1 -W 2 ${ip}`);

    // Check multiple indicators of successful ping
    const isSuccess = stdout.includes('1 received') ||
                     stdout.includes('1 packets transmitted, 1 received') ||
                     stdout.includes('64 bytes from') ||
                     (stdout.includes('transmitted') && stdout.includes('received') && !stdout.includes('0 received'));

    return isSuccess;
  } catch (error) {
    // Ping failed (host unreachable, timeout, etc.)
    return false;
  }
}

function createPreparedStatement(db: DBType) {
  return {
    findByClientId: db.query.equipment
      .findMany({
        where: eq(equipment.clientId, sql.placeholder('clientId')),
      })
      .prepare(),
    findById: db.query.equipment
      .findFirst({ where: eq(equipment.id, sql.placeholder('id')) })
      .prepare(),
    findByClientAndIp: db.query.equipment
      .findFirst({
        where: and(
          eq(equipment.clientId, sql.placeholder('clientId')),
          eq(equipment.ipAddress, sql.placeholder('ipAddress'))
        ),
      })
      .prepare(),
    delete: db
      .delete(equipment)
      .where(eq(equipment.id, sql.placeholder('id')))
      .prepare(),
  };
}

export class EquipmentService {
  #db: DBType;
  #statements: ReturnType<typeof createPreparedStatement>;

  constructor(db: DBType) {
    this.#db = db;
    this.#statements = createPreparedStatement(db);
  }

  async getByClientId(clientId: ID) {
    const result = await this.#statements.findByClientId.execute({ clientId });

    return result.map((row) => ({
      ...row,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
    }));
  }

  async getByClientIdWithStatus(clientId: ID) {
    const result = await this.#statements.findByClientId.execute({ clientId });

    // Check status for each equipment by pinging the IP address
    const equipmentWithStatus = await Promise.all(
      result.map(async (row) => {
        const isOnline = await pingIP(row.ipAddress);
        return {
          ...row,
          status: isOnline ? 'Online' : 'Offline',
          createdAt: new Date(row.createdAt),
          updatedAt: new Date(row.updatedAt),
        };
      })
    );

    return equipmentWithStatus;
  }

  async getById(id: ID) {
    const result = await this.#statements.findById.execute({ id });
    if (!result) return null;

    return {
      ...result,
      createdAt: new Date(result.createdAt),
      updatedAt: new Date(result.updatedAt),
    };
  }

  // Expose pingIP method for external use
  async pingIP(ipAddress: string): Promise<boolean> {
    return await pingIP(ipAddress);
  }

  get(id: ID) {
    return this.#statements.findById.execute({ id });
  }

  async create(data: EquipmentCreateType) {
    // Check for duplicate IP address within the same client
    const existing = await this.#statements.findByClientAndIp.execute({
      clientId: data.clientId,
      ipAddress: data.ipAddress,
    });

    if (existing) {
      throw new Error('Equipment with this IP address already exists for this client');
    }

    // Ensure IP has /32 suffix if it doesn't have a CIDR notation
    const ipAddress = data.ipAddress.includes('/') ? data.ipAddress : `${data.ipAddress}/32`;

    const result = await this.#db
      .insert(equipment)
      .values({
        ...data,
        ipAddress,
      })
      .returning()
      .execute();

    return result[0];
  }

  async update(id: ID, data: UpdateEquipmentType) {
    // If updating IP address, check for duplicates
    if (data.ipAddress) {
      const current = await this.get(id);
      if (!current) {
        throw new Error('Equipment not found');
      }

      const ipAddress = data.ipAddress.includes('/') ? data.ipAddress : `${data.ipAddress}/32`;

      const existing = await this.#statements.findByClientAndIp.execute({
        clientId: current.clientId,
        ipAddress,
      });

      if (existing && existing.id !== id) {
        throw new Error('Equipment with this IP address already exists for this client');
      }

      data.ipAddress = ipAddress;
    }

    // Filter out undefined values to only update provided fields
    const updateData = Object.fromEntries(
      Object.entries(data).filter(([_, value]) => value !== undefined)
    );

    if (Object.keys(updateData).length > 0) {
      await this.#db.update(equipment).set(updateData).where(eq(equipment.id, id)).execute();
    }
  }

  delete(id: ID) {
    return this.#statements.delete.execute({ id });
  }

  async syncWithServerAllowedIps(clientId: ID, serverAllowedIps: string[]) {
    // Get current equipment for this client
    const currentEquipment = await this.getByClientId(clientId);
    
    // Create a map of current equipment by IP
    const currentEquipmentMap = new Map(
      currentEquipment.map(eq => [eq.ipAddress, eq])
    );

    // Process each IP in serverAllowedIps
    for (const ip of serverAllowedIps) {
      const ipWithCidr = ip.includes('/') ? ip : `${ip}/32`;
      
      if (!currentEquipmentMap.has(ipWithCidr)) {
        // Create new equipment entry for IPs not in equipment table
        await this.create({
          clientId,
          ipAddress: ipWithCidr,
          name: '',
          description: '',
          enabled: true,
        });
      }
    }

    // Remove equipment entries that are no longer in serverAllowedIps
    const serverAllowedIpsWithCidr = serverAllowedIps.map(ip => 
      ip.includes('/') ? ip : `${ip}/32`
    );
    
    for (const eq of currentEquipment) {
      if (!serverAllowedIpsWithCidr.includes(eq.ipAddress)) {
        await this.delete(eq.id);
      }
    }
  }
}
