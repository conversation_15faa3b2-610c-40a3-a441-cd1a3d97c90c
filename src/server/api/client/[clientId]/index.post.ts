import {
  ClientGetSchema,
  ClientUpdateSchema,
} from '#db/repositories/client/types';

export default definePermissionEventHandler(
  'clients',
  'update',
  async ({ event, checkPermissions }) => {
    const { clientId } = await getValidatedRouterParams(
      event,
      validateZod(ClientGetSchema, event)
    );

    const data = await readValidatedBody(
      event,
      validateZod(ClientUpdateSchema, event)
    );

    const client = await Database.clients.get(clientId);
    checkPermissions(client);

    // If serverAllowedIps was updated, handle route changes
    if (data.serverAllowedIps) {
      const oldServerAllowedIps = client.serverAllowedIps || [];
      const newServerAllowedIps = data.serverAllowedIps;

      // Find added and removed IPs
      const addedIps = newServerAllowedIps.filter(ip => !oldServerAllowedIps.includes(ip));
      const removedIps = oldServerAllowedIps.filter(ip => !newServerAllowedIps.includes(ip));

      // Remove routes for deleted IPs
      for (const ip of removedIps) {
        const ipWithCidr = ip.includes('/') ? ip : `${ip}/32`;
        await WireGuard.removeEquipmentRoute(ipWithCidr);
      }

      // Update database
      await Database.clients.update(clientId, data);
      await Database.equipment.syncWithServerAllowedIps(clientId, data.serverAllowedIps);

      // Add routes for new IPs
      for (const ip of addedIps) {
        const ipWithCidr = ip.includes('/') ? ip : `${ip}/32`;
        await WireGuard.addEquipmentRoute(ipWithCidr, client.ipv4Address);
      }

      // Save config
      await WireGuard.saveConfig();
    } else {
      // For other changes, just update and save config
      await Database.clients.update(clientId, data);
      await WireGuard.saveConfig();
    }

    return { success: true };
  }
);
