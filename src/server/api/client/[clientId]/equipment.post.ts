import { 
  ClientEquipmentGetSchema,
  EquipmentCreateSchema 
} from '#db/repositories/equipment/types';

export default definePermissionEventHandler(
  'clients',
  'update',
  async ({ event, checkPermissions }) => {
    const { clientId } = await getValidatedRouterParams(
      event,
      validateZod(ClientEquipmentGetSchema, event)
    );

    const data = await readValidatedBody(
      event,
      validateZod(EquipmentCreateSchema, event)
    );

    const client = await Database.clients.get(clientId);
    checkPermissions(client);

    if (!client) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Client not found',
      });
    }

    try {
      const equipment = await Database.equipment.create({
        clientId,
        ...data,
      });

      // Update client's serverAllowedIps to include the new equipment IP
      const currentEquipment = await Database.equipment.getByClientId(clientId);
      const serverAllowedIps = currentEquipment.map(eq => eq.ipAddress);

      await Database.clients.update(clientId, {
        ...client,
        serverAllowedIps,
      });

      // Save config and add route for new equipment
      await WireGuard.saveConfig();
      await WireGuard.addEquipmentRoute(equipment.ipAddress, client.ipv4Address);

      return equipment;
    } catch (error) {
      if (error instanceof Error && error.message.includes('already exists')) {
        throw createError({
          statusCode: 409,
          statusMessage: 'Equipment with this IP address already exists',
        });
      }
      throw error;
    }
  }
);
