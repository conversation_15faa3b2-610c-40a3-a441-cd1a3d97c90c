import { EquipmentGetSchema } from '#db/repositories/equipment/types';

export default definePermissionEventHandler(
  'clients',
  'update',
  async ({ event, checkPermissions }) => {
    const { equipmentId } = await getValidatedRouterParams(
      event,
      validateZod(EquipmentGetSchema, event)
    );

    const equipment = await Database.equipment.get(equipmentId);
    if (!equipment) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Equipment not found',
      });
    }

    const client = await Database.clients.get(equipment.clientId);
    checkPermissions(client);

    // Remove route before deleting equipment
    await WireGuard.removeEquipmentRoute(equipment.ipAddress);

    await Database.equipment.delete(equipmentId);

    // Update client's serverAllowedIps
    const currentEquipment = await Database.equipment.getByClientId(equipment.clientId);
    const serverAllowedIps = currentEquipment.map(eq => eq.ipAddress);

    await Database.clients.update(equipment.clientId, {
      ...client,
      serverAllowedIps,
    });

    // Save config (no need to restart for route removal)
    await WireGuard.saveConfig();

    return { success: true };
  }
);
