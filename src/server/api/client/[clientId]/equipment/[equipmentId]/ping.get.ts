import { ClientEquipmentGetSchema } from '#db/repositories/equipment/types';

export default definePermissionEventHandler(
  'clients',
  'view',
  async ({ event }) => {
    const clientId = getRouterParam(event, 'clientId');
    const equipmentId = getRouterParam(event, 'equipmentId');

    if (!clientId || !equipmentId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Client ID and Equipment ID are required',
      });
    }

    // Get the specific equipment
    const equipment = await Database.equipment.getById(Number(equipmentId));
    if (!equipment || equipment.clientId !== Number(clientId)) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Equipment not found',
      });
    }

    // Ping the equipment and measure latency
    const startTime = Date.now();
    const isOnline = await Database.equipment.pingIP(equipment.ipAddress);
    const latency = Date.now() - startTime;

    return {
      id: equipment.id,
      status: isOnline ? 'Online' : 'Offline',
      latency: isOnline ? latency : null,
    };
  }
);