import { ClientEquipmentGetSchema } from '#db/repositories/equipment/types';

export default definePermissionEventHandler(
  'clients',
  'view',
  async ({ event }) => {
    const { clientId } = await getValidatedRouterParams(
      event,
      validateZod(ClientEquipmentGetSchema, event)
    );

    // Get equipment list
    const equipmentList = await Database.equipment.getByClientId(clientId);

    // Check status for each equipment with latency measurement
    const equipmentWithStatus = await Promise.all(
      equipmentList.map(async (equipment) => {
        const startTime = Date.now();
        const isOnline = await Database.equipment.pingIP(equipment.ipAddress);
        const latency = Date.now() - startTime;

        return {
          id: equipment.id,
          status: isOnline ? 'Online' : 'Offline',
          latency: isOnline ? latency : null,
        };
      })
    );

    return equipmentWithStatus;
  }
);
