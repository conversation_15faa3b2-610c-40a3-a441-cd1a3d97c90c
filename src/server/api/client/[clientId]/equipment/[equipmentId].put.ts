import { 
  EquipmentGetSchema,
  EquipmentUpdateSchema 
} from '#db/repositories/equipment/types';

export default definePermissionEventHandler(
  'clients',
  'update',
  async ({ event, checkPermissions }) => {
    const { equipmentId } = await getValidatedRouterParams(
      event,
      validateZod(EquipmentGetSchema, event)
    );

    const data = await readValidatedBody(
      event,
      validateZod(EquipmentUpdateSchema, event)
    );

    const equipment = await Database.equipment.get(equipmentId);
    if (!equipment) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Equipment not found',
      });
    }

    const client = await Database.clients.get(equipment.clientId);
    checkPermissions(client);

    try {
      // If IP address is being updated, remove old route first
      if (data.ipAddress && data.ipAddress !== equipment.ipAddress) {
        await WireGuard.removeEquipmentRoute(equipment.ipAddress);
      }

      await Database.equipment.update(equipmentId, data);

      // Update client's serverAllowedIps
      const currentEquipment = await Database.equipment.getByClientId(equipment.clientId);
      const serverAllowedIps = currentEquipment.map(eq => eq.ipAddress);

      await Database.clients.update(equipment.clientId, {
        ...client,
        serverAllowedIps,
      });

      // Save config and add route for updated equipment
      await WireGuard.saveConfig();
      if (data.ipAddress && data.ipAddress !== equipment.ipAddress) {
        const newIpAddress = data.ipAddress.includes('/') ? data.ipAddress : `${data.ipAddress}/32`;
        await WireGuard.addEquipmentRoute(newIpAddress, client.ipv4Address);
      }

      return { success: true };
    } catch (error) {
      if (error instanceof Error && error.message.includes('already exists')) {
        throw createError({
          statusCode: 409,
          statusMessage: 'Equipment with this IP address already exists',
        });
      }
      throw error;
    }
  }
);
