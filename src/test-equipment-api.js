// Test script for equipment API endpoints
const BASE_URL = 'http://localhost:3000/api';

async function testEquipmentAPI() {
  console.log('Testing Equipment API...');
  
  try {
    // Test 1: Get client equipment (should return empty array initially)
    console.log('\n1. Testing GET /api/client/1/equipment');
    const getResponse = await fetch(`${BASE_URL}/client/1/equipment`);
    console.log('Status:', getResponse.status);
    if (getResponse.ok) {
      const equipment = await getResponse.json();
      console.log('Equipment:', equipment);
    } else {
      console.log('Error:', await getResponse.text());
    }

    // Test 2: Add new equipment
    console.log('\n2. Testing POST /api/client/1/equipment');
    const addResponse = await fetch(`${BASE_URL}/client/1/equipment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ipAddress: '*************',
        name: 'Test Device',
        description: 'Test device description'
      })
    });
    console.log('Status:', addResponse.status);
    if (addResponse.ok) {
      const newEquipment = await addResponse.json();
      console.log('New Equipment:', newEquipment);
    } else {
      console.log('Error:', await addResponse.text());
    }

    // Test 3: Get equipment again (should show the new equipment)
    console.log('\n3. Testing GET /api/client/1/equipment (after adding)');
    const getResponse2 = await fetch(`${BASE_URL}/client/1/equipment`);
    console.log('Status:', getResponse2.status);
    if (getResponse2.ok) {
      const equipment = await getResponse2.json();
      console.log('Equipment:', equipment);
    } else {
      console.log('Error:', await getResponse2.text());
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test if this script is executed directly
if (typeof window === 'undefined') {
  testEquipmentAPI();
}
