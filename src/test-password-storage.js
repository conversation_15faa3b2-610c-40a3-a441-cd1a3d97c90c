#!/usr/bin/env node

// Simple test script to verify plaintext password storage functionality
// This script tests that:
// 1. Plaintext passwords are stored correctly
// 2. Plaintext passwords are not exposed in public user objects
// 3. Internal methods can access plaintext passwords

import { drizzle } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import { eq } from 'drizzle-orm';
import * as schema from './server/database/schema.ts';
import { UserService } from './server/database/repositories/user/service.ts';

// Create a fresh test database
const client = createClient({ url: 'file:./test-password-storage.db' });
const db = drizzle({ client, schema });

async function testPasswordStorage() {
  console.log('🧪 Testing plaintext password storage functionality...\n');

  // Initialize database schema
  console.log('📋 Initializing database schema...');
  try {
    await db.run(`
      CREATE TABLE IF NOT EXISTS users_table (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        password_plaintext TEXT,
        email TEXT,
        name TEXT NOT NULL,
        role INTEGER NOT NULL,
        totp_key TEXT,
        totp_verified INTEGER NOT NULL,
        enabled INTEGER NOT NULL,
        created_at TEXT NOT NULL DEFAULT (CURRENT_TIMESTAMP),
        updated_at TEXT NOT NULL DEFAULT (CURRENT_TIMESTAMP)
      )
    `);
    console.log('✅ Database schema initialized');
  } catch (schemaError) {
    console.log('⚠️ Schema initialization error (may be expected):', schemaError.message);
  }

  const userService = new UserService(db);
  const testUsername = 'testuser_' + Date.now();
  const testPassword = 'TestPassword123!';

  try {
    // Test 1: Create user with plaintext password storage
    console.log('1️⃣ Testing user creation with plaintext password storage...');
    await userService.create(testUsername, testPassword);
    console.log('✅ User created successfully');

    // Test 2: Verify public user object doesn't contain plaintext password
    console.log('\n2️⃣ Testing that public user object excludes plaintext password...');
    const publicUser = await userService.getByUsername(testUsername);
    
    if (publicUser && !('passwordPlaintext' in publicUser)) {
      console.log('✅ Plaintext password is NOT exposed in public user object');
    } else {
      console.log('❌ SECURITY ISSUE: Plaintext password is exposed in public user object!');
      return;
    }

    // Test 3: Verify internal method can access plaintext password
    console.log('\n3️⃣ Testing internal plaintext password access...');
    const plaintextPassword = await userService.getPlaintextPassword(publicUser.id);
    
    if (plaintextPassword === testPassword) {
      console.log('✅ Internal method can access plaintext password correctly');
    } else {
      console.log('❌ Internal method cannot access plaintext password or password mismatch');
      return;
    }

    // Test 4: Test password update
    console.log('\n4️⃣ Testing password update with plaintext storage...');
    const newPassword = 'NewTestPassword456!';

    // Debug: Check current user data
    const currentUser = await db.query.user.findFirst({ where: eq(schema.user.id, publicUser.id) });
    console.log('Current user before update:', currentUser ? 'found' : 'not found');

    await userService.updatePassword(publicUser.id, testPassword, newPassword);
    
    const updatedPlaintextPassword = await userService.getPlaintextPassword(publicUser.id);
    if (updatedPlaintextPassword === newPassword) {
      console.log('✅ Password update with plaintext storage works correctly');
    } else {
      console.log('❌ Password update with plaintext storage failed');
      return;
    }

    // Test 5: Verify login still works and returns sanitized user
    console.log('\n5️⃣ Testing login with sanitized user response...');
    const loginResult = await userService.login(testUsername, newPassword, undefined);
    
    if (loginResult.success && loginResult.user && !('passwordPlaintext' in loginResult.user)) {
      console.log('✅ Login works and returns sanitized user object');
    } else {
      console.log('❌ Login failed or returns unsanitized user object');
      return;
    }

    console.log('\n🎉 All tests passed! Plaintext password storage is working correctly and securely.');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  } finally {
    // Cleanup: Remove test user
    try {
      await db.delete(schema.user).where(eq(schema.user.username, testUsername));
      console.log('\n🧹 Test user cleaned up');
    } catch (cleanupError) {
      console.log('\n⚠️ Failed to cleanup test user:', cleanupError.message);
    }
  }
}

// Run the test
testPasswordStorage().catch(console.error);
