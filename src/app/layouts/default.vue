<template>
  <div class="flex h-screen">
    <!-- Sidebar for logged in users -->
    <UiSidebar v-if="loggedIn" />

    <!-- Main content area -->
    <div
      class="p-2 flex flex-1 flex-col transition-all duration-300"
      :class="loggedIn ? (globalStore.sidebarCollapsed ? 'ml-16' : 'ml-64') : ''"
    >
      <!-- Header -->
      <header
        class="flex-shrink-0"
        :class="loggedIn ? 'mx-auto mt-4 max-w-5xl px-4' : 'mx-auto mt-4 max-w-3xl'"
      >
        <div
          class="mb-5 w-full"
          :class="
            loggedIn
              ? 'flex flex-col items-center justify-between sm:flex-row'
              : 'flex justify-end'
          "
        >
          <div class="flex flex-row gap-3">
            <HeaderLangSelector />
            <HeaderThemeSwitch />
          </div>
        </div>
      </header>

      <!-- Main content -->
      <main class="flex-1 overflow-auto">
        <slot />
      </main>

      <!-- Footer -->
      <UiFooter class="flex-shrink-0" />
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const globalStore = useGlobalStore();

const loggedIn = computed(() => route.path !== '/login');
</script>
