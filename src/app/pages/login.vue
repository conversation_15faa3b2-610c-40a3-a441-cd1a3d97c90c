<template>
  <main class="min-h-0 via-white to-indigo-50 dark:from-neutral-900 dark:via-neutral-800 dark:to-neutral-900">
    <!-- Header with security warning -->
    <HeaderInsecure />

    <!-- Main content container -->
    <div class="flex items-center justify-center px-4 py-16 sm:px-6 lg:px-8">
      <div class="w-full max-w-md">
        <!-- Login form card -->
        <div class="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-xl dark:bg-neutral-900/80 dark:border-neutral-700/50">
          <!-- Decorative gradient background -->
          <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-indigo-500/5 dark:from-blue-400/10 dark:to-indigo-400/10"></div>

          <!-- Content -->
          <div class="relative px-8 py-10">
            <!-- Logo and welcome section -->
            <div class="text-center mb-8">
              <div class="mb-6 flex items-center justify-center">
                <div class="relative">
                  <img
                    src="/logo.png"
                    width="200"
                    alt="WG Easy"
                    class="h-auto max-w-full"
                  />
                  <!-- Subtle glow effect -->
                  <div class="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-lg blur-xl -z-10"></div>
                </div>
              </div>

              <div class="space-y-2">
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                  {{ $t('login.welcome') }}
                </h1>
                <p class="text-sm text-gray-600 dark:text-neutral-400">
                  {{ $t('login.signInToAccount') }}
                </p>
              </div>
            </div>

            <!-- Login form -->
            <form class="space-y-5" @submit.prevent="submit">
              <!-- Username field -->
              <div class="space-y-1">
                <BaseInput
                  id="username"
                  v-model="username"
                  type="text"
                  name="username"
                  :label="$t('general.username')"
                  autocomplete="username"
                  autofocus
                  :floating-label="true"
                  :error="!!usernameError"
                  :error-message="usernameError"
                  class="transition-all duration-200"
                />
              </div>

              <!-- Password field -->
              <div class="space-y-1">
                <BaseInput
                  id="password"
                  v-model="password"
                  type="password"
                  name="password"
                  :label="$t('general.password')"
                  autocomplete="current-password"
                  :floating-label="true"
                  :error="!!passwordError"
                  :error-message="passwordError"
                  class="transition-all duration-200"
                />
              </div>

              <!-- 2FA field (conditional) -->
              <div v-if="totpRequired" class="space-y-1">
                <BaseInput
                  id="totp"
                  v-model="totp"
                  type="text"
                  name="totp"
                  :label="$t('general.2faCode')"
                  autocomplete="one-time-code"
                  inputmode="numeric"
                  maxlength="6"
                  pattern="\d{6}"
                  :floating-label="true"
                  :error="!!totpError"
                  :error-message="totpError"
                  class="transition-all duration-200"
                />
              </div>

              <!-- Remember me checkbox -->
              <div class="flex items-center justify-between py-2">
                <label
                  class="flex items-center gap-3 cursor-pointer group"
                  :title="$t('login.rememberMeDesc')"
                >
                  <BaseSwitch v-model="remember" id="remember" />
                  <span class="text-sm text-gray-700 dark:text-neutral-300 group-hover:text-gray-900 dark:group-hover:text-neutral-100 transition-colors">
                    {{ $t('login.rememberMe') }}
                  </span>
                </label>
              </div>

              <!-- Submit button -->
              <div class="pt-2">
                <BaseGoogleButton
                  type="submit"
                  variant="primary"
                  :disabled="!isFormValid"
                  :loading="authenticating"
                  full-width
                  class="h-12 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {{ $t('login.signIn') }}
                </BaseGoogleButton>
              </div>
            </form>

            <!-- Additional security info -->
            <div class="mt-8 pt-6 border-t border-gray-200/50 dark:border-neutral-700/50">
              <div class="flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-neutral-500">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <span>{{ $t('login.secureConnection') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
const authStore = useAuthStore();
authStore.update();

const toast = useToast();
const { t } = useI18n();

// Form state
const authenticating = ref(false);
const remember = ref(false);
const username = ref<string>('');
const password = ref<string>('');
const totpRequired = ref(false);
const totp = ref<string>('');

// Error states
const usernameError = ref<string>('');
const passwordError = ref<string>('');
const totpError = ref<string>('');

// Form validation
const isFormValid = computed(() => {
  const hasUsername = username.value.trim().length >= 2;
  const hasPassword = password.value.length >= 12;
  const hasTotpIfRequired = !totpRequired.value || totp.value.length === 6;

  return hasUsername && hasPassword && hasTotpIfRequired && !authenticating.value;
});

// Clear errors when user types
watch(username, () => {
  if (usernameError.value) usernameError.value = '';
});

watch(password, () => {
  if (passwordError.value) passwordError.value = '';
});

watch(totp, () => {
  if (totpError.value) totpError.value = '';
});

// Validate form fields
const validateForm = () => {
  let isValid = true;

  // Clear previous errors
  usernameError.value = '';
  passwordError.value = '';
  totpError.value = '';

  // Username validation
  if (!username.value.trim()) {
    usernameError.value = t('zod.user.username');
    isValid = false;
  } else if (username.value.trim().length < 2) {
    usernameError.value = t('zod.user.username');
    isValid = false;
  }

  // Password validation
  if (!password.value) {
    passwordError.value = t('zod.user.password');
    isValid = false;
  } else if (password.value.length < 12) {
    passwordError.value = t('zod.user.password');
    isValid = false;
  }

  // TOTP validation (if required)
  if (totpRequired.value) {
    if (!totp.value) {
      totpError.value = t('zod.user.totpCode');
      isValid = false;
    } else if (totp.value.length !== 6 || !/^\d{6}$/.test(totp.value)) {
      totpError.value = t('zod.user.totpCode');
      isValid = false;
    }
  }

  return isValid;
};

const _submit = useSubmit(
  '/api/session',
  {
    method: 'post',
  },
  {
    revert: async (success, data) => {
      if (success) {
        if (data?.status === 'success') {
          await navigateTo('/');
        } else if (data?.status === 'TOTP_REQUIRED') {
          authenticating.value = false;
          totpRequired.value = true;
          toast.showToast({
            title: t('general.2fa'),
            message: t('login.2faRequired'),
            type: 'error',
          });
          return;
        } else if (data?.status === 'INVALID_TOTP_CODE') {
          authenticating.value = false;
          totp.value = '';
          totpError.value = t('login.2faWrong');
          return;
        }
      } else {
        // Handle general authentication errors
        passwordError.value = 'Invalid username or password';
      }
      authenticating.value = false;
      if (!totpRequired.value) {
        password.value = '';
      }
    },
    noSuccessToast: true,
  }
);

async function submit() {
  if (!validateForm() || authenticating.value) return;

  authenticating.value = true;

  return _submit({
    username: username.value.trim(),
    password: password.value,
    remember: remember.value,
    totpCode: totpRequired.value ? totp.value : undefined,
  });
}
</script>
