<template>
  <main v-if="data" class="bg-gray-50 dark:bg-neutral-900">
    <div class="container mx-auto max-w-4xl px-4 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-neutral-100">{{ $t('hooks.title') }}</h1>
        <p class="mt-1 text-gray-600 dark:text-neutral-400">Configure WireGuard interface hooks and scripts</p>
      </div>

      <FormElement @submit.prevent="submit">
        <div class="space-y-8">
          <!-- Pre-Connection Hooks Section -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
            <div class="relative p-6">
              <div class="mb-5 flex items-center space-x-3">
                <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">Pre-Connection Hooks</h2>
                  <p class="text-sm text-gray-500 dark:text-neutral-400">Scripts executed before interface operations</p>
                </div>
              </div>
              <div class="space-y-6">
                <FormTextField
                  id="PreUp"
                  v-model="data.preUp"
                  :label="$t('hooks.preUp')"
                  :description="$t('hooks.preUpDesc')"
                />
                <FormTextField
                  id="PreDown"
                  v-model="data.preDown"
                  :label="$t('hooks.preDown')"
                  :description="$t('hooks.preDownDesc')"
                />
              </div>
            </div>
          </div>

          <!-- Post-Connection Hooks Section -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
            <div class="relative p-6">
              <div class="mb-5 flex items-center space-x-3">
                <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">Post-Connection Hooks</h2>
                  <p class="text-sm text-gray-500 dark:text-neutral-400">Scripts executed after interface operations</p>
                </div>
              </div>
              <div class="space-y-6">
                <FormTextField
                  id="PostUp"
                  v-model="data.postUp"
                  :label="$t('hooks.postUp')"
                  :description="$t('hooks.postUpDesc')"
                />
                <FormTextField
                  id="PostDown"
                  v-model="data.postDown"
                  :label="$t('hooks.postDown')"
                  :description="$t('hooks.postDownDesc')"
                />
              </div>
            </div>
          </div>

          <!-- Information Section -->
          <div class="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
            <div class="flex items-start space-x-3">
              <svg class="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Hook Script Information</h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                  <ul class="list-disc list-inside space-y-1">
                    <li><strong>PreUp:</strong> Executed before the interface is brought up</li>
                    <li><strong>PostUp:</strong> Executed after the interface is brought up</li>
                    <li><strong>PreDown:</strong> Executed before the interface is brought down</li>
                    <li><strong>PostDown:</strong> Executed after the interface is brought down</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Bar -->
        <div class="mt-8 flex flex-col-reverse gap-3 sm:flex-row sm:justify-end">
          <button
            type="button"
            @click="revert"
            class="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:hover:border-neutral-500"
          >
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ $t('form.revert') }}
          </button>
          <button
            type="submit"
            class="group inline-flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-2.5 text-sm font-medium text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95"
          >
            <svg class="h-4 w-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            {{ $t('form.save') }}
          </button>
        </div>
      </FormElement>
    </div>
  </main>
</template>

<script setup lang="ts">
const { data: _data, refresh } = await useFetch(`/api/admin/hooks`, {
  method: 'get',
});

const data = toRef(_data.value);

const _submit = useSubmit(
  `/api/admin/hooks`,
  {
    method: 'post',
  },
  { revert }
);

async function submit() {
  return _submit(data.value);
}

async function revert() {
  await refresh();
  data.value = toRef(_data.value).value;
}
</script>
