<template>
  <main v-if="data" class="bg-gray-50 dark:bg-neutral-900">
    <div class="container mx-auto max-w-4xl px-4 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-neutral-100">{{ $t('admin.config.title') }}</h1>
        <p class="mt-1 text-gray-600 dark:text-neutral-400">Configure WireGuard server settings</p>
      </div>

      <FormElement @submit.prevent="submit">
        <div class="space-y-8">
          <!-- Connection Configuration Section -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
            <div class="relative p-6">
              <div class="mb-5 flex items-center space-x-3">
                <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">{{ $t('admin.config.connection') }}</h2>
                  <p class="text-sm text-gray-500 dark:text-neutral-400">Configure server connection settings</p>
                </div>
              </div>
              <div class="space-y-6">
                <FormHostField
                  id="host"
                  v-model="data.host"
                  :label="$t('general.host')"
                  :description="$t('admin.config.hostDesc')"
                  url="/api/admin/ip-info"
                />
                <FormNumberField
                  id="port"
                  v-model="data.port"
                  :label="$t('general.port')"
                  :description="$t('admin.config.portDesc')"
                />
              </div>
            </div>
          </div>

          <!-- Network Configuration Section -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
            <div class="relative p-6">
              <div class="mb-5 flex items-center space-x-3">
                <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">Network Settings</h2>
                  <p class="text-sm text-gray-500 dark:text-neutral-400">Configure default network settings for clients</p>
                </div>
              </div>
              <div class="space-y-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                    {{ $t('general.allowedIps') }}
                  </label>
                  <p class="text-sm text-gray-500 dark:text-neutral-400 mb-3">{{ $t('admin.config.allowedIpsDesc') }}</p>
                  <BaseTagsInput
                    v-model="data.defaultAllowedIps"
                    placeholder="0.0.0.0/0, ::/0"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                    {{ $t('general.dns') }}
                  </label>
                  <p class="text-sm text-gray-500 dark:text-neutral-400 mb-3">{{ $t('admin.config.dnsDesc') }}</p>
                  <BaseTagsInput
                    v-model="data.defaultDns"
                    placeholder="*******, *******"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Advanced Configuration Section -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
            <div class="relative p-6">
              <div class="mb-5 flex items-center space-x-3">
                <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">{{ $t('form.sectionAdvanced') }}</h2>
                  <p class="text-sm text-gray-500 dark:text-neutral-400">Advanced network configuration options</p>
                </div>
              </div>
              <div class="space-y-6">
                <div class="grid gap-6 lg:grid-cols-2">
                  <FormNumberField
                    id="defaultMtu"
                    v-model="data.defaultMtu"
                    :label="$t('general.mtu')"
                    :description="$t('admin.config.mtuDesc')"
                  />
                  <FormNumberField
                    id="defaultPersistentKeepalive"
                    v-model="data.defaultPersistentKeepalive"
                    :label="$t('general.persistentKeepalive')"
                    :description="$t('admin.config.persistentKeepaliveDesc')"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Bar -->
        <div class="mt-8 flex flex-col-reverse gap-3 sm:flex-row sm:justify-end">
          <button
            type="button"
            @click="revert"
            class="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:hover:border-neutral-500"
          >
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ $t('form.revert') }}
          </button>
          <button
            type="submit"
            class="group inline-flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-2.5 text-sm font-medium text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95"
          >
            <svg class="h-4 w-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            {{ $t('form.save') }}
          </button>
        </div>
      </FormElement>
    </div>
  </main>
</template>

<script lang="ts" setup>
const { data: _data, refresh } = await useFetch(`/api/admin/userconfig`, {
  method: 'get',
});

const data = toRef(_data.value);

const _submit = useSubmit(
  `/api/admin/userconfig`,
  {
    method: 'post',
  },
  { revert }
);

function submit() {
  return _submit(data.value);
}

async function revert() {
  await refresh();
  data.value = toRef(_data.value).value;
}
</script>
