<template>
  <main v-if="data" class="bg-gray-50 dark:bg-neutral-900">
    <div class="container mx-auto max-w-4xl px-4 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-neutral-100">{{ $t('admin.general.title') }}</h1>
        <p class="mt-1 text-gray-600 dark:text-neutral-400">Configure general system settings</p>
      </div>

      <FormElement @submit.prevent="submit">
        <div class="space-y-8">
          <!-- Session Configuration Section -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
            <div class="relative p-6">
              <div class="mb-5 flex items-center space-x-3">
                <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">Session Settings</h2>
                  <p class="text-sm text-gray-500 dark:text-neutral-400">Configure session timeout settings</p>
                </div>
              </div>
              <div class="space-y-4">
                <FormNumberField
                  id="session"
                  v-model="data.sessionTimeout"
                  :label="$t('admin.general.sessionTimeout')"
                  :description="$t('admin.general.sessionTimeoutDesc')"
                />
              </div>
            </div>
          </div>

          <!-- Metrics Configuration Section -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
            <div class="relative p-6">
              <div class="mb-5 flex items-center space-x-3">
                <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">{{ $t('admin.general.metrics') }}</h2>
                  <p class="text-sm text-gray-500 dark:text-neutral-400">Configure metrics and monitoring settings</p>
                </div>
              </div>
              <div class="space-y-6">
                <FormTextField
                  id="password"
                  v-model="data.metricsPassword"
                  :label="$t('admin.general.metricsPassword')"
                  :description="$t('admin.general.metricsPasswordDesc')"
                />
                <div class="grid gap-6 lg:grid-cols-2">
                  <FormSwitchField
                    id="prometheus"
                    v-model="data.metricsPrometheus"
                    :label="$t('admin.general.prometheus')"
                    :description="$t('admin.general.prometheusDesc')"
                  />
                  <FormSwitchField
                    id="json"
                    v-model="data.metricsJson"
                    :label="$t('admin.general.json')"
                    :description="$t('admin.general.jsonDesc')"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Bar -->
        <div class="mt-8 flex flex-col-reverse gap-3 sm:flex-row sm:justify-end">
          <button
            type="button"
            @click="revert"
            class="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:hover:border-neutral-500"
          >
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ $t('form.revert') }}
          </button>
          <button
            type="submit"
            class="group inline-flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-2.5 text-sm font-medium text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95"
          >
            <svg class="h-4 w-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            {{ $t('form.save') }}
          </button>
        </div>
      </FormElement>
    </div>
  </main>
</template>

<script setup lang="ts">
const { data: _data, refresh } = await useFetch(`/api/admin/general`, {
  method: 'get',
});
const data = toRef(_data.value);

const _submit = useSubmit(
  `/api/admin/general`,
  {
    method: 'post',
  },
  { revert }
);

function submit() {
  return _submit(data.value);
}

async function revert() {
  await refresh();
  data.value = toRef(_data.value).value;
}
</script>
