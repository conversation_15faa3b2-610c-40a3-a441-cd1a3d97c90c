<template>
  <main class="bg-gray-50 dark:bg-neutral-900">
    <div class="container mx-auto max-w-4xl px-4 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-neutral-100">{{ $t('pages.me') }}</h1>
        <p class="mt-1 text-gray-600 dark:text-neutral-400">Manage your account settings and preferences</p>
      </div>

      <div class="space-y-8">
        <!-- General Information Section -->
        <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
          <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
          <div class="relative p-6">
            <div class="mb-5 flex items-center space-x-3">
              <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">{{ $t('form.sectionGeneral') }}</h2>
                <p class="text-sm text-gray-500 dark:text-neutral-400">Update your personal information</p>
              </div>
            </div>

            <form @submit.prevent="submit" class="space-y-6">
              <div class="grid gap-6 lg:grid-cols-2">
                <FormTextField
                  id="name"
                  v-model="name"
                  :label="$t('general.name')"
                />
                <FormTextField
                  id="email"
                  v-model="email"
                  :label="$t('user.email')"
                />
              </div>

              <div class="flex justify-end">
                <button
                  type="submit"
                  class="group inline-flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-2.5 text-sm font-medium text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95"
                >
                  <svg class="h-4 w-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  {{ $t('form.save') }}
                </button>
              </div>
            </form>
          </div>
        </div>
        <!-- Password Security Section -->
        <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
          <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
          <div class="relative p-6">
            <div class="mb-5 flex items-center space-x-3">
              <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <div>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">{{ $t('general.password') }}</h2>
                <p class="text-sm text-gray-500 dark:text-neutral-400">Change your account password</p>
              </div>
            </div>

            <form @submit.prevent="updatePassword" class="space-y-6">
              <div class="grid gap-6 lg:grid-cols-2">
                <FormPasswordField
                  id="current-password"
                  v-model="currentPassword"
                  autocomplete="current-password"
                  :label="$t('me.currentPassword')"
                />
                <FormPasswordField
                  id="new-password"
                  v-model="newPassword"
                  autocomplete="new-password"
                  :label="$t('general.newPassword')"
                />
                <FormPasswordField
                  id="confirm-password"
                  v-model="confirmPassword"
                  autocomplete="new-password"
                  :label="$t('general.confirmPassword')"
                />
              </div>

              <div class="flex justify-end">
                <button
                  type="submit"
                  class="group inline-flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-2.5 text-sm font-medium text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95"
                >
                  <svg class="h-4 w-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 0 00-8 0v4h8z" />
                  </svg>
                  {{ $t('general.updatePassword') }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
     </div>
  </main>
</template>

<script setup lang="ts">
const authStore = useAuthStore();
authStore.update();

const name = ref(authStore.userData?.name);
const email = ref(authStore.userData?.email);

const _submit = useSubmit(
  `/api/me`,
  {
    method: 'post',
  },
  {
    revert: () => {
      return authStore.update();
    },
  }
);

function submit() {
  return _submit({ name: name.value, email: email.value });
}

const currentPassword = ref('');
const newPassword = ref('');
const confirmPassword = ref('');

const _updatePassword = useSubmit(
  `/api/me/password`,
  {
    method: 'post',
  },
  {
    revert: async () => {
      currentPassword.value = '';
      newPassword.value = '';
      confirmPassword.value = '';
    },
  }
);

function updatePassword() {
  return _updatePassword({
    currentPassword: currentPassword.value,
    newPassword: newPassword.value,
    confirmPassword: confirmPassword.value,
  });
}
</script>
