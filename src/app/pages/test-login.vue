<template>
  <div class="min-h-screen bg-gray-50 dark:bg-neutral-900 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
      <h1 class="text-3xl font-bold text-center text-blue-600 dark:text-blue-400">🔵 蓝色主题组件展示</h1>

      <!-- Header Components -->
      <div class="bg-white dark:bg-neutral-800 rounded-lg p-6 shadow-lg">
        <h2 class="text-xl font-semibold mb-4 text-blue-600 dark:text-blue-400">Header 组件</h2>
        <div class="flex items-center gap-4 justify-center">
          <div class="text-center">
            <p class="text-sm mb-2">语言选择器</p>
            <HeaderLangSelector />
          </div>
          <div class="text-center">
            <p class="text-sm mb-2">主题切换器</p>
            <HeaderThemeSwitch />
          </div>
          <div class="text-center">
            <p class="text-sm mb-2">图表开关</p>
            <HeaderChartToggle />
          </div>
        </div>
      </div>

      <!-- Base Components -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Buttons -->
        <div class="bg-white dark:bg-neutral-800 rounded-lg p-6 shadow-lg">
          <h2 class="text-lg font-semibold mb-4 text-blue-600 dark:text-blue-400">按钮组件</h2>
          <div class="space-y-3">
            <BasePrimaryButton class="w-full">主要按钮 (蓝色)</BasePrimaryButton>
            <BaseSecondaryButton class="w-full">次要按钮 (蓝色边框)</BaseSecondaryButton>
            <BaseGoogleButton variant="primary" full-width>Google 主要按钮</BaseGoogleButton>
            <BaseGoogleButton variant="secondary" full-width>Google 次要按钮</BaseGoogleButton>
          </div>
        </div>

        <!-- Form Components -->
        <div class="bg-white dark:bg-neutral-800 rounded-lg p-6 shadow-lg">
          <h2 class="text-lg font-semibold mb-4 text-blue-600 dark:text-blue-400">表单组件</h2>
          <div class="space-y-4">
            <BaseInput
              id="test-input"
              v-model="testValue"
              label="测试输入框"
              :floating-label="true"
              placeholder="蓝色焦点状态..."
            />
            <div class="flex items-center gap-3">
              <BaseSwitch v-model="switchValue" id="test-switch" />
              <label for="test-switch" class="text-sm">蓝色开关: {{ switchValue ? '开启' : '关闭' }}</label>
            </div>
            <BaseSelect v-model="selectValue" :options="selectOptions" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const testValue = ref('');
const loading = ref(false);

const toggleLoading = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 2000);
};
</script>
