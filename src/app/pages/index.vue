<template>
  <main class="bg-gray-50 dark:bg-neutral-900">
    <div class="container mx-auto max-w-4xl px-4 py-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-neutral-100">
              {{ $t('pages.clients') }}
            </h1>
            <p class="mt-1 text-gray-600 dark:text-neutral-400">
              Manage your WireGuard client configurations
            </p>
          </div>
          <div class="flex items-center space-x-3">
            <!-- Chart Controls -->
            <div class="flex items-center space-x-2">
              <HeaderChartToggle />
              <HeaderChartTypeSelector v-if="globalStore.uiShowCharts" />
            </div>

            <!-- Client Controls -->
            <div class="h-6 w-px bg-gray-300 dark:bg-neutral-600"></div>
            <ClientsSort />
            <ClientsNew />
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
        <div class="relative">
          <!-- Clients List -->
          <div v-if="clientsStore.clients && clientsStore.clients.length > 0">
            <ClientsList />
          </div>

          <!-- Empty State -->
          <div v-else-if="clientsStore.clients && clientsStore.clients.length === 0">
            <ClientsEmpty />
          </div>

          <!-- Loading State -->
          <div
            v-else-if="clientsStore.clients === null"
            class="flex items-center justify-center py-16"
          >
            <div class="text-center">
              <IconsLoading class="mx-auto h-8 w-8 animate-spin text-blue-600 dark:text-blue-400" />
              <p class="mt-4 text-sm text-gray-500 dark:text-neutral-400">
                Loading clients...
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
const authStore = useAuthStore();
authStore.update();

const globalStore = useGlobalStore();
const clientsStore = useClientsStore();

// TODO?: use hover card to show more detailed info without leaving the page
// or do something like a accordion

const intervalId = ref<NodeJS.Timeout | null>(null);

clientsStore.refresh();

onMounted(() => {
  // TODO?: replace with websocket or similar
  intervalId.value = setInterval(() => {
    clientsStore
      .refresh({
        updateCharts: globalStore.uiShowCharts,
      })
      .catch(console.error);
  }, 1000);
});

onUnmounted(() => {
  if (intervalId.value !== null) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
});
</script>
