<template>
  <div
    :class="[
      'fixed left-0 top-0 z-40 h-full bg-white shadow-lg transition-all duration-300 ease-in-out dark:bg-neutral-800',
      isCollapsed ? 'w-16' : 'w-64'
    ]"
  >
    <!-- Toggle Button -->
    <button
      @click="toggleSidebar"
      class="absolute -right-3 top-6 z-40 flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-white shadow-md transition-all hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
    >
      <svg
        class="h-3 w-3 transition-transform duration-200"
        :class="isCollapsed ? 'rotate-180' : ''"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>

    <!-- Sidebar Content -->
    <div class="flex h-full flex-col">
      <!-- User Profile Section -->
      <div class="border-b border-gray-200 p-4 dark:border-neutral-700">
        <HeaderLogo/>
      </div>

      <!-- Navigation Menu -->
      <nav class="flex-1 space-y-1 p-2">
        <NuxtLink
          to="/"
          class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900 dark:hover:text-blue-100"
          :class="$route.path === '/' ? 'bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-100' : 'text-gray-700 dark:text-neutral-200'"
        >
          <svg class="h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <span
            v-if="!isCollapsed"
            class="ml-3 transition-opacity duration-200"
            :class="isCollapsed ? 'opacity-0' : 'opacity-100'"
          >
            {{ $t('pages.clients') }}
          </span>
        </NuxtLink>

        <NuxtLink
          to="/me"
          class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900 dark:hover:text-blue-100"
          :class="$route.path === '/me' ? 'bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-100' : 'text-gray-700 dark:text-neutral-200'"
        >
          <svg class="h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <span
            v-if="!isCollapsed"
            class="ml-3 transition-opacity duration-200"
            :class="isCollapsed ? 'opacity-0' : 'opacity-100'"
          >
            {{ $t('pages.me') }}
          </span>
        </NuxtLink>

        <!-- Admin Section -->
        <template v-if="authStore.userData && hasPermissions(authStore.userData, 'admin', 'any')">
          <!-- Admin Section Header -->
          <div
            v-if="!isCollapsed"
            class="mt-4 px-3 py-2 text-xs font-semibold uppercase tracking-wide text-gray-500 dark:text-neutral-400"
          >
            {{ $t('pages.admin.panel') }}
          </div>

          <!-- Admin General -->
          <NuxtLink
            to="/admin/general"
            class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900 dark:hover:text-blue-100"
            :class="$route.path === '/admin/general' ? 'bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-100' : 'text-gray-700 dark:text-neutral-200'"
          >
            <svg class="h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
            </svg>
            <span
              v-if="!isCollapsed"
              class="ml-3 transition-opacity duration-200"
              :class="isCollapsed ? 'opacity-0' : 'opacity-100'"
            >
              {{ $t('pages.admin.general') }}
            </span>
          </NuxtLink>

          <!-- Admin Config -->
          <NuxtLink
            to="/admin/config"
            class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900 dark:hover:text-blue-100"
            :class="$route.path === '/admin/config' ? 'bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-100' : 'text-gray-700 dark:text-neutral-200'"
          >
            <svg class="h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span
              v-if="!isCollapsed"
              class="ml-3 transition-opacity duration-200"
              :class="isCollapsed ? 'opacity-0' : 'opacity-100'"
            >
              {{ $t('pages.admin.config') }}
            </span>
          </NuxtLink>

          <!-- Admin Interface -->
          <NuxtLink
            to="/admin/interface"
            class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900 dark:hover:text-blue-100"
            :class="$route.path === '/admin/interface' ? 'bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-100' : 'text-gray-700 dark:text-neutral-200'"
          >
            <svg class="h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span
              v-if="!isCollapsed"
              class="ml-3 transition-opacity duration-200"
              :class="isCollapsed ? 'opacity-0' : 'opacity-100'"
            >
              {{ $t('pages.admin.interface') }}
            </span>
          </NuxtLink>

          <!-- Admin Hooks -->
          <NuxtLink
            to="/admin/hooks"
            class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900 dark:hover:text-blue-100"
            :class="$route.path === '/admin/hooks' ? 'bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-100' : 'text-gray-700 dark:text-neutral-200'"
          >
            <svg class="h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5" />
            </svg>
            <span
              v-if="!isCollapsed"
              class="ml-3 transition-opacity duration-200"
              :class="isCollapsed ? 'opacity-0' : 'opacity-100'"
            >
              {{ $t('pages.admin.hooks') }}
            </span>
          </NuxtLink>
        </template>
      </nav>

      <!-- Logout Button -->
      <div class="border-t border-gray-200 p-2 dark:border-neutral-700">
        <div class="flex items-center mb-2">
          <BaseAvatar class="h-10 w-10 flex-shrink-0">
            {{ fallbackName }}
          </BaseAvatar>
          <div
            v-if="!isCollapsed"
            class="ml-3 min-w-0 flex-1 transition-opacity duration-200"
            :class="isCollapsed ? 'opacity-0' : 'opacity-100'"
          >
            <div class="truncate text-sm font-medium text-gray-900 dark:text-neutral-100">
              {{ authStore.userData?.name }}
            </div>
            <div class="truncate text-xs text-gray-500 dark:text-neutral-400">
              @{{ authStore.userData?.username }}
            </div>
          </div>
        </div>
        <button
          @click="logout"
          class="group flex w-full items-center rounded-lg px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-red-50 hover:text-red-700 dark:text-neutral-200 dark:hover:bg-red-900 dark:hover:text-red-100"
        >
          <svg class="h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          <span
            v-if="!isCollapsed"
            class="ml-3 transition-opacity duration-200"
            :class="isCollapsed ? 'opacity-0' : 'opacity-100'"
          >
            {{ $t('general.logout') }}
          </span>
        </button>
      </div>
    </div>
  </div>

  <!-- Overlay for mobile -->
  <div
    v-if="!isCollapsed && isMobile"
    class="fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden"
    @click="closeSidebar"
  />
</template>

<script setup lang="ts">
const authStore = useAuthStore();
const globalStore = useGlobalStore();
const route = useRoute();

// Sidebar state
const isCollapsed = computed(() => globalStore.sidebarCollapsed);
const isMobile = ref(false);

// Check if mobile
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 1024;
  };
  checkMobile();
  window.addEventListener('resize', checkMobile);
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile);
  });
});

// User avatar fallback
const fallbackName = computed(() => {
  return authStore.userData?.name
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
});

// Functions
function toggleSidebar() {
  globalStore.toggleSidebar();
}

function closeSidebar() {
  if (isMobile.value) {
    globalStore.setSidebarCollapsed(true);
  }
}

// Logout function
const _logout = useSubmit(
  '/api/session',
  {
    method: 'delete',
  },
  {
    revert: async () => {
      await navigateTo('/login');
    },
    noSuccessToast: true,
  }
);

function logout() {
  return _logout(undefined);
}
</script>
