<template>
  <ToggleGroupRoot
    type="single"
    :model-value="theme.preference"
    @update:model-value="setTheme"
    class="inline-flex rounded-lg border border-gray-300 bg-white shadow-sm dark:border-neutral-600 dark:bg-neutral-800"
  >
    <ToggleGroupItem
      value="light"
      class="flex h-8 w-8 items-center justify-center rounded-l-lg transition-all hover:bg-blue-50 data-[state=on]:bg-blue-100 data-[state=on]:text-blue-900 dark:hover:bg-blue-900 dark:data-[state=on]:bg-blue-800 dark:data-[state=on]:text-blue-100"
      :title="$t('theme.light')"
    >
      <IconsSun class="h-4 w-4" :class="theme.preference === 'light' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-neutral-400'" />
    </ToggleGroupItem>
    <ToggleGroupItem
      value="system"
      class="flex h-8 w-8 items-center justify-center border-x border-gray-300 transition-all hover:bg-blue-50 data-[state=on]:bg-blue-100 data-[state=on]:text-blue-900 dark:border-neutral-600 dark:hover:bg-blue-900 dark:data-[state=on]:bg-blue-800 dark:data-[state=on]:text-blue-100"
      :title="$t('theme.system')"
    >
      <IconsHalfMoon class="h-4 w-4" :class="theme.preference === 'system' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-neutral-400'" />
    </ToggleGroupItem>
    <ToggleGroupItem
      value="dark"
      class="flex h-8 w-8 items-center justify-center rounded-r-lg transition-all hover:bg-blue-50 data-[state=on]:bg-blue-100 data-[state=on]:text-blue-900 dark:hover:bg-blue-900 dark:data-[state=on]:bg-blue-800 dark:data-[state=on]:text-blue-100"
      :title="$t('theme.dark')"
    >
      <IconsMoon class="h-4 w-4" :class="theme.preference === 'dark' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-neutral-400'" />
    </ToggleGroupItem>
  </ToggleGroupRoot>
</template>

<script lang="ts" setup>
const theme = useTheme();

function setTheme(value: string | undefined) {
  if (value && ['light', 'dark', 'system'].includes(value)) {
    theme.preference = value as 'light' | 'dark' | 'system';
  }
}
</script>
