<template>
  <SelectRoot v-model="langProxy" :default-value="locale">
    <SelectTrigger
      class="inline-flex h-8 items-center justify-around gap-2 rounded-lg border border-gray-300 bg-white px-3 text-sm leading-none shadow-sm transition-all hover:bg-gray-50 hover:border-blue-300 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:hover:border-blue-400 dark:focus:border-blue-400 dark:focus:ring-blue-400"
      aria-label="Select language"
    >
      <IconsLanguage class="size-3 text-blue-600 dark:text-blue-400" />
      <SelectValue class="font-medium" />
      <IconsArrowDown class="size-3 text-gray-500 dark:text-neutral-400" />
    </SelectTrigger>

    <SelectPortal>
      <SelectContent
        class="z-50 min-w-32 rounded-lg border border-gray-200 bg-white shadow-lg dark:border-neutral-600 dark:bg-neutral-800"
        position="popper"
        :side-offset="4"
      >
        <SelectViewport class="p-1">
          <SelectItem
            v-for="(option, index) in langs"
            :key="index"
            :value="option.code"
            class="relative flex h-8 items-center rounded-md px-3 text-sm leading-none outline-none cursor-pointer transition-colors hover:bg-blue-50 hover:text-blue-900 focus:bg-blue-50 focus:text-blue-900 data-[state=checked]:bg-blue-100 data-[state=checked]:text-blue-900 data-[state=checked]:font-medium dark:text-neutral-200 dark:hover:bg-blue-900 dark:hover:text-blue-100 dark:focus:bg-blue-900 dark:focus:text-blue-100 dark:data-[state=checked]:bg-blue-800 dark:data-[state=checked]:text-blue-100"
          >
            <SelectItemText>
              {{ option.name }}
            </SelectItemText>
            <SelectItemIndicator class="absolute right-2">
              <svg class="h-3 w-3 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </SelectItemIndicator>
          </SelectItem>
        </SelectViewport>
      </SelectContent>
    </SelectPortal>
  </SelectRoot>
</template>

<script setup lang="ts">
const { locales, locale, setLocale } = useI18n();

const langProxy = ref(locale);

watchEffect(() => {
  setLocale(langProxy.value);
});

const langs = locales.value.sort((a, b) => a.code.localeCompare(b.code));
</script>
