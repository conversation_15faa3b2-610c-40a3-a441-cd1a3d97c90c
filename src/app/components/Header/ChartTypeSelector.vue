<template>
  <DropdownMenuRoot>
    <DropdownMenuTrigger as-child>
      <button
        class="group inline-flex h-9 w-9 cursor-pointer items-center justify-center whitespace-nowrap rounded-lg bg-white border border-gray-200 shadow-sm transition-all duration-200 hover:bg-blue-50 hover:border-blue-300 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 dark:bg-neutral-800 dark:border-neutral-600 dark:hover:bg-blue-900/20 dark:hover:border-blue-600 dark:focus:ring-blue-400"
        :title="'Chart Type: ' + (chartTypes.find(t => t.type === globalStore.uiChartType)?.label || 'Area Chart')"
      >
        <component
          :is="currentChartIcon"
          class="h-4 w-4 text-gray-600 transition-colors duration-200 group-hover:text-blue-600 dark:text-neutral-400 dark:group-hover:text-blue-400"
        />
      </button>
    </DropdownMenuTrigger>
    
    <DropdownMenuPortal>
      <DropdownMenuContent
        class="z-50 min-w-[180px] rounded-lg border border-gray-200 bg-white p-2 shadow-xl backdrop-blur-sm dark:border-neutral-600 dark:bg-neutral-800"
        :side-offset="8"
        :align="'end'"
      >
        <div class="mb-2 px-2 py-1">
          <h3 class="text-xs font-medium text-gray-500 uppercase tracking-wide dark:text-neutral-400">
            Chart Type
          </h3>
        </div>

        <DropdownMenuItem
          v-for="chartType in chartTypes"
          :key="chartType.type"
          class="group flex cursor-pointer items-center gap-3 rounded-md px-3 py-2.5 text-sm transition-all duration-200 hover:bg-blue-50 focus:bg-blue-50 focus:outline-none dark:hover:bg-blue-900/20 dark:focus:bg-blue-900/20"
          :class="globalStore.uiChartType === chartType.type ? 'bg-blue-50 dark:bg-blue-900/30' : ''"
          @click="selectChartType(chartType.type)"
        >
          <div
            class="flex h-8 w-8 items-center justify-center rounded-md transition-colors"
            :class="globalStore.uiChartType === chartType.type
              ? 'bg-blue-100 dark:bg-blue-800/50'
              : 'bg-gray-100 group-hover:bg-blue-100 dark:bg-neutral-700 dark:group-hover:bg-blue-800/50'"
          >
            <component
              :is="chartType.icon"
              class="h-4 w-4 transition-colors"
              :class="globalStore.uiChartType === chartType.type
                ? 'text-blue-600 dark:text-blue-400'
                : 'text-gray-500 group-hover:text-blue-600 dark:text-neutral-400 dark:group-hover:text-blue-400'"
            />
          </div>

          <div class="flex flex-1 flex-col">
            <span
              class="font-medium transition-colors"
              :class="globalStore.uiChartType === chartType.type
                ? 'text-blue-700 dark:text-blue-300'
                : 'text-gray-700 group-hover:text-blue-700 dark:text-neutral-200 dark:group-hover:text-blue-300'"
            >
              {{ chartType.label }}
            </span>
            <span
              class="text-xs transition-colors"
              :class="globalStore.uiChartType === chartType.type
                ? 'text-blue-600/70 dark:text-blue-400/70'
                : 'text-gray-500 group-hover:text-blue-600/70 dark:text-neutral-400 dark:group-hover:text-blue-400/70'"
            >
              {{ chartType.description }}
            </span>
          </div>

          <div
            v-if="globalStore.uiChartType === chartType.type"
            class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 dark:bg-blue-500"
          >
            <svg class="h-3 w-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenuPortal>
  </DropdownMenuRoot>
</template>

<script setup lang="ts">
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuRoot,
  DropdownMenuTrigger,
} from 'radix-vue';

// Chart type icons (using heroicons)
import ChartBarIcon from '@heroicons/vue/24/outline/esm/ChartBarIcon';
import ChartPieIcon from '@heroicons/vue/24/outline/esm/ChartPieIcon';
import SignalIcon from '@heroicons/vue/24/outline/esm/SignalIcon';
import CursorArrowRaysIcon from '@heroicons/vue/24/outline/esm/CursorArrowRaysIcon';

const globalStore = useGlobalStore();

const chartTypes = [
  {
    type: 'area' as const,
    label: 'Area Chart',
    description: 'Filled area visualization',
    icon: ChartPieIcon,
  },
  {
    type: 'line' as const,
    label: 'Line Chart',
    description: 'Simple line visualization',
    icon: SignalIcon,
  },
  {
    type: 'spline' as const,
    label: 'Smooth Line',
    description: 'Curved line visualization',
    icon: CursorArrowRaysIcon,
  },
  {
    type: 'bar' as const,
    label: 'Bar Chart',
    description: 'Vertical bar visualization',
    icon: ChartBarIcon,
  },
];

const currentChartIcon = computed(() => {
  const currentType = chartTypes.find(type => type.type === globalStore.uiChartType);
  return currentType?.icon || ChartPieIcon;
});

function selectChartType(type: typeof globalStore.uiChartType) {
  globalStore.uiChartType = type;
}
</script>
