<template>
  <Toggle
    :pressed="globalStore.uiShowCharts"
    :title="globalStore.uiShowCharts ? 'Hide Charts' : 'Show Charts'"
    @update:pressed="globalStore.toggleCharts"
    class="group inline-flex h-9 w-9 cursor-pointer items-center justify-center rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-200
      hover:bg-blue-50 hover:border-blue-300 hover:shadow-md
      data-[state=on]:bg-blue-50 data-[state=on]:border-blue-300 data-[state=on]:shadow-md
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
      dark:bg-neutral-800 dark:border-neutral-600 dark:hover:bg-blue-900/20 dark:hover:border-blue-600
      dark:data-[state=on]:bg-blue-900/30 dark:data-[state=on]:border-blue-600
      dark:focus:ring-blue-400"
  >
    <IconsChart
      class="h-4 w-4 text-gray-600 transition-colors duration-200
        group-data-[state=on]:text-blue-600
        dark:text-neutral-400 dark:group-data-[state=on]:text-blue-400"
    />
  </Toggle>
</template>

<script lang="ts" setup>
const globalStore = useGlobalStore();
</script>
