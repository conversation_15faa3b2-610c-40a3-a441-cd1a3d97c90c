<template>
  <div class="flex w-full flex-wrap items-center gap-2">
    <!-- Existing tags -->
    <div
      v-for="(item, index) in internalValue"
      :key="item"
      class="flex items-center gap-1 rounded bg-blue-100 px-2 py-1 text-blue-900 shadow-sm dark:bg-blue-900 dark:text-blue-100"
    >
      <span class="text-xs font-medium">{{ item }}</span>
      <button
        type="button"
        @click="removeTag(index)"
        class="rounded p-0.5 transition-colors hover:bg-blue-200 dark:hover:bg-blue-800"
      >
        <IconsClose class="h-3 w-3" />
      </button>
    </div>

    <!-- Add button -->
    <button
      v-if="!showInput"
      type="button"
      @click="showAddInput"
      class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-blue-600 transition-colors hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800"
    >
      <IconsPlus class="h-3 w-3" />
    </button>

    <!-- Small input field -->
    <div v-if="showInput" class="flex items-center gap-1">
      <input
        ref="inputRef"
        v-model="newTag"
        type="text"
        :placeholder="placeholder || 'Add item...'"
        class="w-32 rounded border border-gray-300 px-2 py-1 text-xs focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-200 dark:focus:border-blue-400 dark:focus:ring-blue-400"
        @keydown.enter="addTag"
        @keydown.escape="cancelAdd"
        @blur="handleBlur"
      />
      <button
        type="button"
        @click="cancelAdd"
        class="flex h-5 w-5 items-center justify-center rounded bg-gray-100 text-gray-600 transition-colors hover:bg-gray-200 dark:bg-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-600"
      >
        <IconsClose class="h-3 w-3" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  placeholder?: string
}>()

const modelValue = defineModel<string[] | null>()

// Handle null values by converting to empty array for internal use
const internalValue = computed({
  get: () => modelValue.value || [],
  set: (value: string[]) => {
    modelValue.value = value.length > 0 ? value : null
  }
})

const showInput = ref(false)
const newTag = ref('')
const inputRef = ref<HTMLInputElement>()

const showAddInput = () => {
  showInput.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const addTag = () => {
  const trimmedTag = newTag.value.trim()
  if (trimmedTag && !internalValue.value.includes(trimmedTag)) {
    internalValue.value = [...internalValue.value, trimmedTag]
  }
  newTag.value = ''
  showInput.value = false
}

const cancelAdd = () => {
  newTag.value = ''
  showInput.value = false
}

const removeTag = (index: number) => {
  const newValue = [...internalValue.value]
  newValue.splice(index, 1)
  internalValue.value = newValue
}

const handleBlur = () => {
  // Add tag if there's content, otherwise cancel
  if (newTag.value.trim()) {
    addTag()
  } else {
    cancelAdd()
  }
}
</script>
