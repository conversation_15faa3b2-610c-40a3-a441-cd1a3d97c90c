<template>
  <ClientOnly>
    <apexchart
      ref="chartRef"
      width="100%"
      height="100%"
      v-bind="$attrs"
      :options="chartOptions"
      :series="chartSeries"
    />
  </ClientOnly>
</template>

<script setup lang="ts">
import type { VueApexChartsComponent } from 'vue3-apexcharts';

const props = defineProps<{
  options: VueApexChartsComponent['options'];
  series: VueApexChartsComponent['series'];
}>();

const chartRef = ref();

// Memoize chart options to prevent unnecessary re-renders
const chartOptions = computed(() => {
  return {
    ...props.options,
    // Performance optimizations
    chart: {
      ...props.options?.chart,
      redrawOnParentResize: false,
      redrawOnWindowResize: false,
    },
  };
});

// Memoize series data
const chartSeries = computed(() => props.series);

// Expose chart instance for external control
defineExpose({
  chart: chartRef,
  updateOptions: (options: any, redraw = false) => {
    if (chartRef.value) {
      chartRef.value.updateOptions(options, redraw);
    }
  },
  updateSeries: (series: any, animate = true) => {
    if (chartRef.value) {
      chartRef.value.updateSeries(series, animate);
    }
  },
});
</script>
