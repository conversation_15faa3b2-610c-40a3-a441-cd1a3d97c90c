<template>
  <component
    :is="elementType"
    role="button"
    class="inline-flex items-center rounded border-2 border-gray-300 px-3 py-1.5 text-sm text-gray-700 transition hover:border-blue-600 hover:bg-blue-600 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-neutral-600 dark:text-neutral-200 dark:hover:border-blue-500 dark:hover:bg-blue-500"
    v-bind="attrs"
  >
    <slot />
  </component>
</template>

<script setup lang="ts">
const props = defineProps({
  as: {
    type: String,
    default: 'button',
  },
});

const elementType = computed(() => props.as);

const attrs = computed(() => {
  const { as, ...rest } = props;
  return rest;
});
</script>
