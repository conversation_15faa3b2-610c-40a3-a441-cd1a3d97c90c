<template>
  <div class="relative">
    <input
      :id="id"
      v-model="data"
      :type="type"
      :name="name"
      :autocomplete="autocomplete"
      :autofocus="autofocus"
      :maxlength="maxlength"
      :pattern="pattern"
      :inputmode="inputmode"
      :disabled="disabled"
      :class="[
        'peer w-full rounded border-2 bg-transparent px-3 py-1.5 text-sm transition-all duration-200 ease-in-out focus:outline-none',
        error
          ? 'border-blue-500 text-blue-900 focus:border-blue-500 dark:border-blue-400 dark:text-blue-100 dark:focus:border-blue-400'
          : 'border-gray-300 text-gray-900 focus:border-blue-600 dark:border-neutral-600 dark:text-neutral-100 dark:focus:border-blue-400',
        'dark:bg-neutral-800',
        disabled && 'cursor-not-allowed opacity-50'
      ]"
      :placeholder="floatingLabel ? ' ' : placeholder"
      @focus="handleFocus"
      @blur="handleBlur"
    />

    <!-- Floating Label -->
    <label
      v-if="floatingLabel"
      :for="id"
      :class="[
        'absolute left-3 transition-all duration-200 ease-in-out pointer-events-none',
        'peer-focus:text-xs peer-focus:-translate-y-5 peer-focus:translate-x-0',
        'peer-[:not(:placeholder-shown)]:text-xs peer-[:not(:placeholder-shown)]:-translate-y-5 peer-[:not(:placeholder-shown)]:translate-x-0',
        hasValue || isFocused
          ? 'text-xs -translate-y-5 translate-x-0'
          : 'text-sm translate-y-1',
        error
          ? 'text-blue-500 peer-focus:text-blue-500 dark:text-blue-400 dark:peer-focus:text-blue-400'
          : 'text-gray-500 peer-focus:text-blue-600 dark:text-neutral-400 dark:peer-focus:text-blue-400'
      ]"
    >
      {{ label }}
    </label>

    <!-- Error Message -->
    <div
      v-if="error && errorMessage"
      class="mt-1 text-sm text-blue-500 dark:text-blue-400"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  id?: string;
  type?: string;
  name?: string;
  label?: string;
  placeholder?: string;
  autocomplete?: string;
  autofocus?: boolean;
  maxlength?: number;
  pattern?: string;
  inputmode?: string;
  disabled?: boolean;
  floatingLabel?: boolean;
  error?: boolean;
  errorMessage?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  floatingLabel: true,
  error: false,
  disabled: false,
});

const data = defineModel<unknown>();

const isFocused = ref(false);

const hasValue = computed(() => {
  return data.value !== null && data.value !== undefined && data.value !== '';
});

const handleFocus = () => {
  isFocused.value = true;
};

const handleBlur = () => {
  isFocused.value = false;
};
</script>
