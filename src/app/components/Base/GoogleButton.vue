<template>
  <button
    :type="type"
    :disabled="disabled || loading"
    :class="[
      'relative inline-flex items-center justify-center rounded-lg px-6 py-3 text-sm font-medium transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2',
      variant === 'primary' && [
        'bg-blue-600 text-white shadow-md hover:bg-blue-700 hover:shadow-lg focus:ring-blue-500',
        'dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-400',
        disabled && 'cursor-not-allowed opacity-50 hover:bg-blue-600 hover:shadow-md dark:hover:bg-blue-500'
      ],
      variant === 'secondary' && [
        'border border-gray-300 bg-white text-gray-700 shadow-sm hover:bg-gray-50 hover:shadow-md focus:ring-gray-500',
        'dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:ring-neutral-400',
        disabled && 'cursor-not-allowed opacity-50 hover:bg-white hover:shadow-sm dark:hover:bg-neutral-800'
      ],
      variant === 'danger' && [
        'bg-blue-600 text-white shadow-md hover:bg-blue-700 hover:shadow-lg focus:ring-blue-500',
        'dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-400',
        disabled && 'cursor-not-allowed opacity-50 hover:bg-blue-600 hover:shadow-md dark:hover:bg-blue-500'
      ],
      fullWidth && 'w-full',
      size === 'sm' && 'px-4 py-2 text-xs',
      size === 'lg' && 'px-8 py-4 text-base'
    ]"
  >
    <!-- Loading Spinner -->
    <div
      v-if="loading"
      class="absolute inset-0 flex items-center justify-center"
    >
      <svg
        class="h-5 w-5 animate-spin text-current"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
    </div>
    
    <!-- Button Content -->
    <span :class="{ 'opacity-0': loading }">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts">
interface Props {
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
}

withDefaults(defineProps<Props>(), {
  type: 'button',
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  fullWidth: false,
});
</script>
