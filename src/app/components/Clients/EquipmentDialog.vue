<template>
  <DialogRoot :modal="true" v-model:open="dialogOpen">
    <DialogTrigger as-child>
      <slot />
    </DialogTrigger>
    <DialogPortal>
      <DialogOverlay
        class="fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"
      />
      <DialogContent
        class="fixed left-1/2 top-1/2 z-[60] w-full max-w-2xl -translate-x-1/2 -translate-y-1/2 border border-gray-200 bg-white shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-lg dark:border-neutral-600 dark:bg-neutral-800"
      >
        <div class="p-6">
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex flex-col space-y-1.5">
              <DialogTitle
                class="text-lg font-semibold leading-none tracking-tight text-gray-900 dark:text-neutral-100"
              >
                {{ $t('client.equipments') }}
              </DialogTitle>
              <DialogDescription
                class="text-sm text-gray-600 dark:text-neutral-400"
              >
                {{ $t('client.equipmentsDesc') }}
              </DialogDescription>
            </div>
            <button
              @click="refreshEquipmentList"
              :disabled="loading"
              class="inline-flex items-center gap-2 rounded border border-gray-300 bg-white px-3 py-1.5 text-sm text-gray-700 transition hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:hover:border-neutral-500"
            >
              <IconsRefresh class="h-4 w-4" :class="{ 'animate-spin': loading }" />
              {{ $t('general.refresh') }}
            </button>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/20 dark:border-red-800">
            <p class="text-sm text-red-600 dark:text-red-400">{{ error }}</p>
          </div>

          <!-- Loading State -->
          <div v-if="loading" class="flex items-center justify-center py-8">
            <IconsLoading class="h-6 w-6 animate-spin text-blue-600" />
          </div>

          <!-- Equipment Table -->
          <div v-else class="rounded-lg border border-gray-200 dark:border-neutral-700 mb-6">
            <div class="overflow-hidden rounded-lg">
              <table class="w-full">
                <thead class="bg-gray-50 dark:bg-neutral-700">
                  <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-neutral-400">
                      {{ $t('general.status') }}
                    </th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-neutral-400">
                      {{ $t('general.name') }}
                    </th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-neutral-400">
                      {{ $t('general.description') }}
                    </th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-neutral-400">
                      {{ $t('general.ipAddress') }}
                    </th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-neutral-400">
                      {{ $t('general.actions') }}
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200 dark:bg-neutral-800 dark:divide-neutral-700">
                  <tr
                    v-for="equipment in equipmentList"
                    :key="equipment.id"
                    class="hover:bg-gray-50 dark:hover:bg-neutral-700"
                  >
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="flex items-center gap-2">
                        <span
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                          :class="{
                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': equipment.status === 'Online',
                            'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': equipment.status === 'Offline',
                            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': equipment.status === 'Loading'
                          }"
                        >
                          <span v-if="equipment.status === 'Loading'" class="animate-spin mr-1">
                            <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24">
                              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          </span>
                          {{ equipment.status }}
                        </span>
                        <span
                          v-if="equipment.status === 'Online' && equipment.latency !== null"
                          class="text-xs text-gray-500 dark:text-neutral-400"
                        >
                          {{ equipment.latency }}ms
                        </span>
                      </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-neutral-100">
                      {{ equipment.name || '-' }}
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-400">
                      {{ equipment.description || '-' }}
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-neutral-100">
                      {{ equipment.ipAddress }}
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                      <div class="flex items-center gap-2">
                        <ClientsEditEquipmentDialog
                          :equipment="equipment"
                          @updated="(updatedEquipment) => onEquipmentUpdated(equipment.id, updatedEquipment)"
                        >
                          <button
                            class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          >
                            <IconsEdit class="h-4 w-4" />
                          </button>
                        </ClientsEditEquipmentDialog>
                        <button
                          class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          @click="deleteEquipment(equipment.id)"
                        >
                          <IconsDelete class="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="equipmentList.length === 0 && !loading">
                    <td colspan="5" class="px-4 py-8 text-center text-sm text-gray-500 dark:text-neutral-400">
                      {{ $t('client.noEquipments') }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2">
            <ClientsAddEquipmentDialog :client="client" @added="onEquipmentAdded">
              <button
                class="inline-flex items-center gap-2 rounded border-2 border-blue-600 bg-blue-600 px-3 py-1.5 text-sm text-white transition hover:border-blue-700 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <IconsPlus class="h-4 w-4" />
                {{ $t('general.add') }}
              </button>
            </ClientsAddEquipmentDialog>
            <DialogClose as-child>
              <button
                class="inline-flex items-center rounded border-2 border-gray-300 bg-white px-3 py-1.5 text-sm text-gray-700 transition hover:border-gray-400 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-200 dark:hover:border-neutral-500 dark:hover:bg-neutral-700"
              >
                {{ $t('dialog.close') }}
              </button>
            </DialogClose>
          </div>
        </div>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script setup lang="ts">
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from 'radix-vue';

const props = defineProps<{
  client: LocalClient;
}>();

const emit = defineEmits<{
  updated: [];
}>();

// Equipment type definition
type Equipment = {
  id: number;
  ipAddress: string;
  name: string;
  description: string;
  enabled: boolean;
  status: 'Online' | 'Offline';
  createdAt: string;
  updatedAt: string;
};

// Equipment list state
interface EquipmentWithStatus extends Omit<Equipment, 'status'> {
  status: 'Online' | 'Offline' | 'Loading';
  latency?: number | null;
  isLoadingStatus?: boolean;
}

// Dialog state
const dialogOpen = ref(false);

const equipmentList = ref<EquipmentWithStatus[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);
const pingInterval = ref<NodeJS.Timeout | null>(null);
const statusLoadingCount = ref(0);

// Load equipment data from API (without ping status)
const loadEquipment = async () => {
  loading.value = true;
  error.value = null;

  try {
    const data = await $fetch<Omit<Equipment, 'status'>[]>(`/api/client/${props.client.id}/equipment`);

    // Initialize equipment list with 'Loading' status
    equipmentList.value = data.map(equipment => ({
      ...equipment,
      status: 'Loading' as const,
      latency: null,
      isLoadingStatus: true,
    }));

    // Start progressive status loading if equipment list is not empty and dialog is open
    if (equipmentList.value.length > 0 && dialogOpen.value) {
      // Don't await - let it load in background
      loadEquipmentStatusProgressively();
    } else {
      stopPingInterval(); // Stop ping checks if no equipment
    }
  } catch (err) {
    console.error('Failed to load equipment:', err);
    error.value = 'Failed to load equipment';
  } finally {
    loading.value = false;
  }
};

// Refresh equipment list without waiting for ping status
const refreshEquipmentList = async () => {
  loading.value = true;
  error.value = null;

  try {
    const data = await $fetch<Omit<Equipment, 'status'>[]>(`/api/client/${props.client.id}/equipment`);
    // Initialize equipment list with 'Loading' status
    equipmentList.value = data.map(equipment => ({
      ...equipment,
      status: 'Loading' as const,
      latency: null,
      isLoadingStatus: true,
    }));

    // Start progressive status loading in background if dialog is open
    if (equipmentList.value.length > 0 && dialogOpen.value) {
      // Don't await - let it load in background
      loadEquipmentStatusProgressively();
    } else {
      stopPingInterval(); // Stop ping checks if no equipment
    }
  } catch (err) {
    console.error('Failed to refresh equipment:', err);
    error.value = 'Failed to refresh equipment';
  } finally {
    loading.value = false; // Stop loading immediately, don't wait for ping
  }
};

// Load equipment status progressively (one by one)
const loadEquipmentStatusProgressively = async () => {
  statusLoadingCount.value = equipmentList.value.length;

  // Load status for each equipment individually
  const statusPromises = equipmentList.value.map(async (equipment, index) => {
    try {
      // Add a small delay between requests to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, index * 100));

      // Check if dialog is still open before making the request
      if (!dialogOpen.value) {
        statusLoadingCount.value--;
        return;
      }

      const statusData = await $fetch<{ id: number; status: 'Online' | 'Offline'; latency: number | null }>(
        `/api/client/${props.client.id}/equipment/${equipment.id}/ping`
      );

      // Check again if dialog is still open before updating
      if (!dialogOpen.value) {
        statusLoadingCount.value--;
        return;
      }

      // Update the specific equipment status
      const equipmentIndex = equipmentList.value.findIndex(eq => eq.id === equipment.id);
      if (equipmentIndex !== -1) {
        equipmentList.value[equipmentIndex].status = statusData.status;
        equipmentList.value[equipmentIndex].latency = statusData.latency;
        equipmentList.value[equipmentIndex].isLoadingStatus = false;
      }

      statusLoadingCount.value--;
    } catch (err) {
      console.error(`Failed to load status for equipment ${equipment.id}:`, err);

      // Check if dialog is still open before updating
      if (!dialogOpen.value) {
        statusLoadingCount.value--;
        return;
      }

      // Set to offline if ping fails
      const equipmentIndex = equipmentList.value.findIndex(eq => eq.id === equipment.id);
      if (equipmentIndex !== -1) {
        equipmentList.value[equipmentIndex].status = 'Offline';
        equipmentList.value[equipmentIndex].latency = null;
        equipmentList.value[equipmentIndex].isLoadingStatus = false;
      }
      statusLoadingCount.value--;
    }
  });

  // Wait for all status checks to complete
  await Promise.all(statusPromises);

  // Start periodic ping checks after initial load is complete (only if dialog is still open)
  if (dialogOpen.value && equipmentList.value.length > 0) {
    startPingInterval();
  }
};

// Update ping status for all equipment (used for periodic updates)
const updatePingStatus = async () => {
  if (equipmentList.value.length === 0 || !dialogOpen.value) return;

  try {
    const statusData = await $fetch<{ id: number; status: 'Online' | 'Offline'; latency: number | null }[]>(
      `/api/client/${props.client.id}/equipment/ping`
    );

    // Check again if dialog is still open before updating
    if (!dialogOpen.value) return;

    // Update status and latency for each equipment
    statusData.forEach(({ id, status, latency }) => {
      const equipment = equipmentList.value.find(eq => eq.id === id);
      if (equipment) {
        equipment.status = status;
        equipment.latency = latency;
      }
    });
  } catch (err) {
    console.error('Failed to update ping status:', err);
    // Don't show error for ping failures, just keep current status
  }
};

// Start ping status interval
const startPingInterval = () => {
  stopPingInterval(); // Clear any existing interval
  pingInterval.value = setInterval(updatePingStatus, 5000); // Every 5 seconds
};

// Stop ping status interval
const stopPingInterval = () => {
  if (pingInterval.value) {
    clearInterval(pingInterval.value);
    pingInterval.value = null;
  }
};

// Watch for client changes and reload equipment
watch(() => props.client.id, () => {
  if (dialogOpen.value) {
    loadEquipment();
  }
});

// Watch for dialog visibility changes
watch(dialogOpen, (isOpen) => {
  if (isOpen) {
    // Dialog opened - always load equipment
    loadEquipment();
  } else {
    // Dialog closed - stop all ping activities
    stopPingInterval();
  }
});

// Load equipment when component is mounted and dialog is open
onMounted(() => {
  if (dialogOpen.value) {
    loadEquipment();
  }
});

// Cleanup interval on unmount
onUnmounted(() => {
  stopPingInterval();
});

async function onEquipmentAdded(equipment: { ipAddress: string; name: string; description: string }) {
  error.value = null; // Clear previous errors

  try {
    const newEquipment = await $fetch<Omit<Equipment, 'status'>>(`/api/client/${props.client.id}/equipment`, {
      method: 'POST',
      body: equipment
    });

    // Add to local list with initial 'Loading' status
    const newEquipmentWithStatus: EquipmentWithStatus = {
      ...newEquipment,
      status: 'Loading',
      latency: null,
      isLoadingStatus: true,
    };
    equipmentList.value.push(newEquipmentWithStatus);

    // Load status for the new equipment
    try {
      const statusData = await $fetch<{ id: number; status: 'Online' | 'Offline'; latency: number | null }>(
        `/api/client/${props.client.id}/equipment/${newEquipment.id}/ping`
      );

      newEquipmentWithStatus.status = statusData.status;
      newEquipmentWithStatus.latency = statusData.latency;
      newEquipmentWithStatus.isLoadingStatus = false;
    } catch (statusErr) {
      console.error('Failed to load status for new equipment:', statusErr);
      newEquipmentWithStatus.status = 'Offline';
      newEquipmentWithStatus.latency = null;
      newEquipmentWithStatus.isLoadingStatus = false;
    }

    // If this is the first equipment, start ping interval
    if (equipmentList.value.length === 1) {
      startPingInterval(); // Start periodic ping checks
    }

    // Emit update event to refresh client data
    emit('updated');
  } catch (err: any) {
    console.error('Failed to add equipment:', err);

    // Show error message based on status code
    if (err.statusCode === 409) {
      error.value = 'Equipment with this IP address already exists';
    } else {
      error.value = 'Failed to add equipment';
    }
  }
}

async function onEquipmentUpdated(equipmentId: number, updatedEquipment: { ipAddress: string; name: string; description: string }) {
  error.value = null; // Clear previous errors

  try {
    await $fetch(`/api/client/${props.client.id}/equipment/${equipmentId}`, {
      method: 'PUT',
      body: updatedEquipment
    });

    // Update local list
    const index = equipmentList.value.findIndex(eq => eq.id === equipmentId);
    if (index !== -1) {
      equipmentList.value[index] = {
        ...equipmentList.value[index],
        ...updatedEquipment,
        status: 'Loading', // Reset status to loading
        latency: null,
        isLoadingStatus: true,
      };

      // Load new status for the updated equipment
      try {
        const statusData = await $fetch<{ id: number; status: 'Online' | 'Offline'; latency: number | null }>(
          `/api/client/${props.client.id}/equipment/${equipmentId}/ping`
        );

        equipmentList.value[index].status = statusData.status;
        equipmentList.value[index].latency = statusData.latency;
        equipmentList.value[index].isLoadingStatus = false;
      } catch (statusErr) {
        console.error('Failed to load status for updated equipment:', statusErr);
        equipmentList.value[index].status = 'Offline';
        equipmentList.value[index].latency = null;
        equipmentList.value[index].isLoadingStatus = false;
      }
    }

    // Emit update event to refresh client data
    emit('updated');
  } catch (err: any) {
    console.error('Failed to update equipment:', err);

    if (err.statusCode === 409) {
      error.value = 'Equipment with this IP address already exists';
    } else {
      error.value = 'Failed to update equipment';
    }
  }
}

async function deleteEquipment(equipmentId: number) {
  error.value = null; // Clear previous errors

  try {
    await $fetch(`/api/client/${props.client.id}/equipment/${equipmentId}`, {
      method: 'DELETE'
    });

    // Remove from local list
    const index = equipmentList.value.findIndex(eq => eq.id === equipmentId);
    if (index !== -1) {
      equipmentList.value.splice(index, 1);
    }

    // If no equipment left, stop ping interval
    if (equipmentList.value.length === 0) {
      stopPingInterval();
    }

    // Emit update event to refresh client data
    emit('updated');
  } catch (err) {
    console.error('Failed to delete equipment:', err);
    error.value = 'Failed to delete equipment';
  }
}
</script>
