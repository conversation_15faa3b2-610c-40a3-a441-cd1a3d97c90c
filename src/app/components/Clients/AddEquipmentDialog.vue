<template>
  <DialogRoot :modal="true">
    <DialogTrigger as-child>
      <slot />
    </DialogTrigger>
    <DialogPortal>
      <DialogOverlay
        class="fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"
      />
      <DialogContent
        class="fixed left-1/2 top-1/2 z-[60] w-full max-w-md -translate-x-1/2 -translate-y-1/2 border border-gray-200 bg-white shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-lg dark:border-neutral-600 dark:bg-neutral-800"
      >
        <div class="p-6">
          <!-- Header -->
          <div class="flex flex-col space-y-1.5 text-center sm:text-left mb-6">
            <DialogTitle
              class="text-lg font-semibold leading-none tracking-tight text-gray-900 dark:text-neutral-100"
            >
              {{ $t('general.add') }} {{ $t('client.equipments') }}
            </DialogTitle>
            <DialogDescription
              class="text-sm text-gray-600 dark:text-neutral-400"
            >
              {{ $t('client.addEquipmentDesc') }}
            </DialogDescription>
          </div>

          <!-- Form -->
          <form @submit.prevent="handleSubmit" class="space-y-4">
            <!-- IP Address -->
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-700 dark:text-neutral-300">
                {{ $t('general.ipAddress') }} *
              </label>
              <input
                v-model="form.ipAddress"
                type="text"
                placeholder="*************"
                class="w-full rounded border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-neutral-600 dark:bg-neutral-700 dark:text-neutral-100 dark:focus:border-blue-400"
                required
              />
            </div>

            <!-- Name -->
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-700 dark:text-neutral-300">
                {{ $t('general.name') }}
              </label>
              <input
                v-model="form.name"
                type="text"
                placeholder="设备名称"
                class="w-full rounded border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-neutral-600 dark:bg-neutral-700 dark:text-neutral-100 dark:focus:border-blue-400"
              />
            </div>

            <!-- Description -->
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-700 dark:text-neutral-300">
                {{ $t('general.description') }}
              </label>
              <textarea
                v-model="form.description"
                placeholder="设备描述"
                rows="3"
                class="w-full rounded border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-neutral-600 dark:bg-neutral-700 dark:text-neutral-100 dark:focus:border-blue-400"
              />
            </div>

            <!-- Actions -->
            <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 pt-4">
              <DialogClose as-child>
                <button
                  type="button"
                  class="inline-flex items-center rounded border-2 border-gray-300 bg-white px-3 py-1.5 text-sm text-gray-700 transition hover:border-gray-400 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-200 dark:hover:border-neutral-500 dark:hover:bg-neutral-700 mb-2 sm:mb-0"
                >
                  {{ $t('dialog.cancel') }}
                </button>
              </DialogClose>
              <button
                type="submit"
                class="inline-flex items-center gap-2 rounded border-2 border-blue-600 bg-blue-600 px-3 py-1.5 text-sm text-white transition hover:border-blue-700 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <IconsPlus class="h-4 w-4" />
                {{ $t('general.add') }}
              </button>
            </div>
          </form>
        </div>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script setup lang="ts">
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from 'radix-vue';

const props = defineProps<{
  client: LocalClient;
}>();

const emit = defineEmits<{
  added: [equipment: { ipAddress: string; name: string; description: string }];
}>();

const form = reactive({
  ipAddress: '',
  name: '',
  description: '',
});

function handleSubmit() {
  if (!form.ipAddress.trim()) return;
  
  // Add /32 suffix if not present
  const ipAddress = form.ipAddress.includes('/') ? form.ipAddress : `${form.ipAddress}/32`;
  
  emit('added', {
    ipAddress,
    name: form.name.trim(),
    description: form.description.trim(),
  });
  
  // Reset form
  form.ipAddress = '';
  form.name = '';
  form.description = '';
}
</script>
