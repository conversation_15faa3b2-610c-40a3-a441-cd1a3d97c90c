<template>
  <div class="divide-y divide-gray-200/60 dark:divide-neutral-700/60">
    <div
      v-for="client in clientsStore.clients"
      :key="client.id"
      class="relative overflow-hidden transition-colors duration-150 hover:bg-blue-50/50 dark:hover:bg-blue-900/10"
    >
      <ClientCard :client="client" />
    </div>
  </div>
</template>

<script setup lang="ts">
const clientsStore = useClientsStore();
</script>
