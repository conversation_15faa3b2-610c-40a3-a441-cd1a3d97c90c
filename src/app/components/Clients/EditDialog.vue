<template>
  <DialogRoot :modal="true" @update:open="onDialogOpenChange">
    <DialogTrigger :class="triggerClass">
      <slot />
    </DialogTrigger>
    <DialogPortal>
      <DialogOverlay
        class="fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"
      />
      <DialogContent
        class="fixed left-1/2 top-1/2 z-[60] w-full max-w-4xl -translate-x-1/2 -translate-y-1/2 bg-white shadow-2xl duration-300 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-2xl dark:bg-neutral-900"
      >
        <!-- Modern Header with Gradient -->
        <div class="relative bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-5 sm:rounded-t-2xl">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 backdrop-blur-sm">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <div>
                <DialogTitle class="text-xl font-semibold text-white">
                  {{ $t('client.edit') }}
                </DialogTitle>
                <p class="text-sm text-blue-100">{{ clientData?.name }}</p>
              </div>
            </div>
            <DialogClose as-child>
              <button class="flex h-8 w-8 items-center justify-center rounded-full bg-white/20 text-white transition-colors hover:bg-white/30 focus:outline-none focus:ring-2 focus:ring-white/50">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </DialogClose>
          </div>
        </div>

      <div v-if="clientData" class="max-h-[65vh] overflow-y-auto px-6 py-6">
        <FormElement @submit.prevent="submit">
          <!-- Modern Card Layout -->
          <div class="space-y-6">
            <!-- General Section -->
            <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-gray-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
              <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
              <div class="relative p-6">
                <div class="mb-5 flex items-center space-x-3">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                    <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">{{ $t('form.sectionGeneral') }}</h3>
                    <p class="text-sm text-gray-500 dark:text-neutral-400">Basic client information and settings</p>
                  </div>
                </div>
                <div class="space-y-5">
                  <div class="grid grid-cols-1 gap-5 lg:grid-cols-2">
                    <FormTextField
                      id="name"
                      v-model="clientData.name"
                      :label="$t('general.name')"
                    />
                    <FormSwitchField
                      id="enabled"
                      v-model="clientData.enabled"
                      :label="$t('client.enabled')"
                    />
                  </div>
                  <FormDateField
                    id="expiresAt"
                    v-model="clientData.expiresAt"
                    :description="$t('client.expireDateDesc')"
                    :label="$t('client.expireDate')"
                  />
                </div>
              </div>
            </div>

            <!-- Address Section -->
            <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
              <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
              <div class="relative p-6">
                <div class="mb-5 flex items-center space-x-3">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                    <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">{{ $t('client.address') }}</h3>
                    <p class="text-sm text-gray-500 dark:text-neutral-400">Network address configuration</p>
                  </div>
                </div>
                <div class="grid grid-cols-1 gap-5 lg:grid-cols-2">
                  <FormTextField
                    id="ipv4Address"
                    v-model="clientData.ipv4Address"
                    label="IPv4"
                  />
                  <FormTextField
                    id="ipv6Address"
                    v-model="clientData.ipv6Address"
                    label="IPv6"
                  />
                </div>
              </div>
            </div>

            <!-- Network Configuration Section -->
            <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
              <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
              <div class="relative p-6">
                <div class="mb-5 flex items-center space-x-3">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                    <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">Network Configuration</h3>
                    <p class="text-sm text-gray-500 dark:text-neutral-400">Configure allowed IPs and DNS settings</p>
                  </div>
                </div>
                <div class="space-y-6">
                  <!-- Allowed IPs -->
                  <div class="rounded-lg bg-white/50 p-4 dark:bg-neutral-800/50">
                    <div class="mb-3 flex items-center gap-2">
                      <h4 class="font-medium text-gray-900 dark:text-neutral-100">{{ $t('general.allowedIps') }}</h4>
                      <BaseTooltip :text="$t('client.allowedIpsDesc')">
                        <IconsInfo class="size-4 text-gray-400" />
                      </BaseTooltip>
                    </div>
                    <BaseTagsInput
                      v-model="clientData.allowedIps"
                      placeholder="0.0.0.0/0, ::/0"
                    />
                  </div>

                  <!-- Server Allowed IPs -->
                  <div class="rounded-lg bg-white/50 p-4 dark:bg-neutral-800/50">
                    <div class="mb-3 flex items-center gap-2">
                      <h4 class="font-medium text-gray-900 dark:text-neutral-100">{{ $t('client.serverAllowedIps') }}</h4>
                      <BaseTooltip :text="$t('client.serverAllowedIpsDesc')">
                        <IconsInfo class="size-4 text-gray-400" />
                      </BaseTooltip>
                    </div>
                    <BaseTagsInput
                      v-model="clientData.serverAllowedIps"
                      placeholder="********/24, fd42:42:42::/64"
                    />
                  </div>

                  <!-- DNS -->
                  <div class="rounded-lg bg-white/50 p-4 dark:bg-neutral-800/50">
                    <div class="mb-3 flex items-center gap-2">
                      <h4 class="font-medium text-gray-900 dark:text-neutral-100">{{ $t('general.dns') }}</h4>
                      <BaseTooltip :text="$t('client.dnsDesc')">
                        <IconsInfo class="size-4 text-gray-400" />
                      </BaseTooltip>
                    </div>
                    <BaseTagsInput
                      v-model="clientData.dns"
                      placeholder="*******, *******"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Advanced Section -->
            <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
              <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
              <div class="relative p-6">
                <div class="mb-5 flex items-center space-x-3">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                    <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">{{ $t('form.sectionAdvanced') }}</h3>
                    <p class="text-sm text-gray-500 dark:text-neutral-400">Advanced network parameters</p>
                  </div>
                </div>
                <div class="grid grid-cols-1 gap-5 lg:grid-cols-2">
                  <FormNumberField
                    id="mtu"
                    v-model="clientData.mtu"
                    :description="$t('client.mtuDesc')"
                    :label="$t('general.mtu')"
                  />
                  <FormNumberField
                    id="persistentKeepalive"
                    v-model="clientData.persistentKeepalive"
                    :description="$t('client.persistentKeepaliveDesc')"
                    :label="$t('general.persistentKeepalive')"
                  />
                </div>
              </div>
            </div>

            <!-- Hooks Section -->
            <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-white border border-gray-200/60 shadow-sm transition-all duration-200 hover:shadow-md hover:border-blue-200/60 dark:from-neutral-800 dark:to-neutral-800/50 dark:border-neutral-700/60 dark:hover:border-blue-600/30">
              <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
              <div class="relative p-6">
                <div class="mb-5 flex items-center space-x-3">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                    <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">{{ $t('client.hooks') }}</h3>
                    <p class="text-sm text-gray-500 dark:text-neutral-400">Custom scripts and automation</p>
                    <BaseTooltip :text="$t('client.hooksDescription')">
                      <IconsInfo class="size-4 text-gray-400 mt-1" />
                    </BaseTooltip>
                  </div>
                </div>
                <div class="grid grid-cols-1 gap-5">
                  <FormTextField
                    id="PreUp"
                    v-model="clientData.preUp"
                    :description="$t('client.hooksLeaveEmpty')"
                    :label="$t('hooks.preUp')"
                  />
                  <FormTextField
                    id="PostUp"
                    v-model="clientData.postUp"
                    :description="$t('client.hooksLeaveEmpty')"
                    :label="$t('hooks.postUp')"
                  />
                  <FormTextField
                    id="PreDown"
                    v-model="clientData.preDown"
                    :description="$t('client.hooksLeaveEmpty')"
                    :label="$t('hooks.preDown')"
                  />
                  <FormTextField
                    id="PostDown"
                    v-model="clientData.postDown"
                    :description="$t('client.hooksLeaveEmpty')"
                    :label="$t('hooks.postDown')"
                  />
                </div>
              </div>
            </div>
          </div>
        </FormElement>
      </div>
      <div v-else class="flex items-center justify-center py-8">
        <IconsLoading class="h-6 w-6 animate-spin text-blue-600" />
      </div>

      <!-- Modern Action Bar -->
      <div class="border-t border-gray-200/60 bg-gradient-to-t from-gray-50/50 to-white px-6 py-4 dark:border-neutral-700/60 dark:from-neutral-800/50 dark:to-neutral-900">
        <div class="flex flex-col-reverse gap-3 sm:flex-row sm:justify-between sm:items-center">
          <!-- Danger Zone -->
          <ClientsDeleteDialog
            :client-name="clientData?.name || ''"
            @delete="deleteClient"
          >
            <button
              type="button"
              class="group inline-flex items-center justify-center gap-2 rounded-lg border-2 border-red-200 bg-red-50 px-4 py-2.5 text-sm font-medium text-red-700 transition-all duration-200 hover:border-red-300 hover:bg-red-100 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:border-red-800 dark:bg-red-900/20 dark:text-red-400 dark:hover:border-red-700 dark:hover:bg-red-900/30"
            >
              <svg class="h-4 w-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              {{ $t('form.delete') }}
            </button>
          </ClientsDeleteDialog>

          <!-- Primary Actions -->
          <div class="flex flex-col-reverse gap-3 sm:flex-row">
            <DialogClose as-child>
              <button class="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:hover:border-neutral-500">
                {{ $t('dialog.cancel') }}
              </button>
            </DialogClose>
            <button
              @click="revert"
              class="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:hover:border-neutral-500"
            >
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              {{ $t('form.revert') }}
            </button>
            <DialogClose as-child>
              <button
                @click="submit"
                class="group inline-flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-2.5 text-sm font-medium text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95"
              >
                <svg class="h-4 w-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                {{ $t('form.save') }}
              </button>
            </DialogClose>
          </div>
        </div>
      </div>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script setup lang="ts">
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from 'radix-vue';

const props = defineProps<{
  client: LocalClient;
  triggerClass?: string;
}>();

const emit = defineEmits<{
  updated: [];
  deleted: [];
}>();

const clientsStore = useClientsStore();

// Reactive client data
const clientData = ref<LocalClient | null>(null);

// Load client data when dialog opens
const loadClientData = async () => {
  try {
    const data = await $fetch(`/api/client/${props.client.id}`);
    clientData.value = { ...data };
  } catch (error) {
    console.error('Failed to load client data:', error);
  }
};

// Handle dialog open/close state changes
const onDialogOpenChange = (open: boolean) => {
  if (open) {
    // Dialog is opening, load fresh client data
    loadClientData();
  }
};

// Watch for client prop changes (in case the same dialog is reused for different clients)
watch(() => props.client.id, () => {
  // Only reload if dialog is currently open
  if (clientData.value) {
    loadClientData();
  }
});

// Submit form
const _submit = useSubmit(
  `/api/client/${props.client.id}`,
  {
    method: 'post',
  },
  {
    revert: async (success) => {
      if (success) {
        await clientsStore.refresh();
        emit('updated');
      }
    },
  }
);

const submit = () => {
  if (clientData.value) {
    return _submit(clientData.value);
  }
};

// Revert changes
const revert = async () => {
  await loadClientData();
};

// Delete client
const _deleteClient = useSubmit(
  `/api/client/${props.client.id}`,
  {
    method: 'delete',
  },
  {
    revert: async () => {
      await clientsStore.refresh();
      emit('deleted');
    },
  }
);

const deleteClient = () => {
  return _deleteClient(undefined);
};
</script>
