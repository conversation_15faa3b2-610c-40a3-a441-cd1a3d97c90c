<template>
  <ClientsEditDialog :client="client" @updated="onUpdated" @deleted="onDeleted">
    <div
      class="rounded bg-gray-100 p-1.5 align-middle transition hover:bg-blue-600 hover:text-white dark:bg-neutral-600 dark:text-neutral-300 dark:hover:bg-blue-600 dark:hover:text-white"
      :title="$t('client.edit')"
    >
      <IconsEdit class="w-5" />
    </div>
  </ClientsEditDialog>
</template>

<script setup lang="ts">
defineProps<{
  client: LocalClient;
}>();

const clientsStore = useClientsStore();

const onUpdated = () => {
  // Client data will be refreshed by the EditDialog
};

const onDeleted = () => {
  // Client will be removed from the list by the EditDialog
};
</script>
