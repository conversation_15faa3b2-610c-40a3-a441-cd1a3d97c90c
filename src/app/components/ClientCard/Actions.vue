<template>
  <DropdownMenuRoot>
    <DropdownMenuTrigger as-child>
      <button
        class="rounded bg-gray-100 p-1.5 align-middle transition hover:bg-blue-600 hover:text-white dark:bg-neutral-600 dark:text-neutral-300 dark:hover:bg-blue-600 dark:hover:text-white"
        :title="$t('client.actions')"
      >
        <IconsEllipsisVertical class="w-5" />
      </button>
    </DropdownMenuTrigger>

    <DropdownMenuPortal>
      <DropdownMenuContent
        :side-offset="5"
        class="z-50 min-w-48 w-max rounded-lg bg-white shadow-lg ring-1 ring-black/5 dark:bg-neutral-800 dark:ring-white/10 overflow-hidden"
      >
        <!-- Edit -->
        <DropdownMenuItem as-child>
          <ClientsEditDialog :client="client" @updated="onUpdated" @deleted="onDeleted" trigger-class="w-full">
            <div class="flex w-full items-center gap-2 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-50 dark:text-gray-200 dark:hover:bg-neutral-700 cursor-pointer whitespace-nowrap">
              <IconsEdit class="h-4 w-4 flex-shrink-0" />
              {{ $t('client.edit') }}
            </div>
          </ClientsEditDialog>
        </DropdownMenuItem>

        <!-- QR Code -->
        <DropdownMenuItem as-child>
          <ClientsQRCodeDialog :qr-code="`./api/client/${client.id}/qrcode.svg`">
            <div class="flex w-full items-center gap-2 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-50 dark:text-gray-200 dark:hover:bg-neutral-700 cursor-pointer whitespace-nowrap">
              <IconsQRCode class="h-4 w-4 flex-shrink-0" />
              {{ $t('client.showQR') }}
            </div>
          </ClientsQRCodeDialog>
        </DropdownMenuItem>

        <!-- Download Config -->
        <DropdownMenuItem as-child>
          <a
            :href="'/api/client/' + client.id + '/configuration'"
            download
            class="flex w-full items-center gap-2 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-50 dark:text-gray-200 dark:hover:bg-neutral-700 cursor-pointer whitespace-nowrap"
            :title="$t('client.downloadConfig')"
          >
            <IconsDownload class="h-4 w-4 flex-shrink-0" />
            {{ $t('client.downloadConfig') }}
          </a>
        </DropdownMenuItem>

        <!-- One Time Link -->
        <DropdownMenuItem as-child>
          <button
            class="flex w-full items-center gap-2 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-50 dark:text-gray-200 dark:hover:bg-neutral-700 cursor-pointer whitespace-nowrap"
            :title="$t('client.otlDesc')"
            @click="showOneTimeLink"
          >
            <IconsLink class="h-4 w-4 flex-shrink-0" />
            {{ $t('client.otlDesc') }}
          </button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenuPortal>
  </DropdownMenuRoot>
</template>

<script setup lang="ts">
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuRoot,
  DropdownMenuTrigger,
} from 'radix-vue';

const props = defineProps<{
  client: LocalClient;
}>();

const clientsStore = useClientsStore();

const onUpdated = () => {
  // Client data will be refreshed by the EditDialog
};

const onDeleted = () => {
  // Client will be removed from the list by the EditDialog
};

const _showOneTimeLink = useSubmit(
  `/api/client/${props.client.id}/generateOneTimeLink`,
  {
    method: 'post',
  },
  {
    revert: async () => {
      await clientsStore.refresh();
    },
    noSuccessToast: true,
  }
);

function showOneTimeLink() {
  return _showOneTimeLink(undefined);
}
</script>
