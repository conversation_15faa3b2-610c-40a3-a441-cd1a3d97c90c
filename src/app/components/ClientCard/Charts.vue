<template>
  <div class="absolute inset-0 z-0 overflow-hidden rounded-lg">
    <!-- TX Chart (Bottom) -->
    <div
      :class="`absolute bottom-0 left-0 right-0 z-0 transition-all duration-300 ${
        globalStore.uiChartType === 'line' ? 'line-chart h-6' : 'h-6 sm:h-8 md:h-10'
      }`"
    >
      <BaseChart
        :options="chartOptionsTX"
        :series="client.transferTxSeries"
        class="chart-container"
      />
    </div>

    <!-- RX Chart (Top) -->
    <div
      :class="`absolute left-0 right-0 top-0 z-0 transition-all duration-300 ${
        globalStore.uiChartType === 'line' ? 'line-chart h-6' : 'h-6 sm:h-8 md:h-10'
      }`"
    >
      <BaseChart
        :options="chartOptionsRX"
        :series="client.transferRxSeries"
        style="transform: scaleY(-1)"
        class="chart-container"
      />
    </div>

    <!-- Chart overlay for better visual separation -->
    <div class="absolute inset-0 z-10 pointer-events-none bg-gradient-to-r from-transparent via-white/5 to-transparent dark:via-black/5" />
  </div>
</template>

<script setup lang="ts">
import type { ApexOptions } from 'apexcharts';

defineProps<{
  client: LocalClient;
}>();

const globalStore = useGlobalStore();
const theme = useTheme();

const chartOptionsTX = computed(() => {
  const opts = {
    ...chartOptions,
    colors: [CHART_COLORS.tx[theme.value]],
  };

  // Handle spline chart type
  opts.chart.type = globalStore.uiChartType === 'spline' ? 'line' : globalStore.uiChartType;
  opts.stroke.width = UI_CHART_PROPS[globalStore.uiChartType]?.strokeWidth || 0;
  opts.stroke.curve = globalStore.uiChartType === 'spline' ? 'smooth' : 'straight';

  // Enhanced gradient configuration for TX
  if (opts.fill && opts.fill.gradient) {
    opts.fill.gradient.gradientToColors = CHART_COLORS.gradient[theme.value].tx;
    opts.fill.gradient.shadeIntensity = 0.3;
    opts.fill.gradient.opacityFrom = 0.8;
    opts.fill.gradient.opacityTo = 0.1;
  }

  return opts;
});

const chartOptionsRX = computed(() => {
  const opts = {
    ...chartOptions,
    colors: [CHART_COLORS.rx[theme.value]],
  };

  // Handle spline chart type
  opts.chart.type = globalStore.uiChartType === 'spline' ? 'line' : globalStore.uiChartType;
  opts.stroke.width = UI_CHART_PROPS[globalStore.uiChartType]?.strokeWidth || 0;
  opts.stroke.curve = globalStore.uiChartType === 'spline' ? 'smooth' : 'straight';

  // Enhanced gradient configuration for RX
  if (opts.fill && opts.fill.gradient) {
    opts.fill.gradient.gradientToColors = CHART_COLORS.gradient[theme.value].rx;
    opts.fill.gradient.shadeIntensity = 0.3;
    opts.fill.gradient.opacityFrom = 0.8;
    opts.fill.gradient.opacityTo = 0.1;
  }

  return opts;
});

const chartOptions = {
  chart: {
    type: undefined as ApexChart['type'],
    background: 'transparent',
    stacked: false,
    toolbar: {
      show: false,
    },
    animations: {
      enabled: true,
      easing: 'easeinout',
      speed: 800,
      animateGradually: {
        enabled: true,
        delay: 150
      },
      dynamicAnimation: {
        enabled: true,
        speed: 350
      }
    },
    parentHeightOffset: 0,
    sparkline: {
      enabled: true,
    },
  },
  colors: [],
  stroke: {
    curve: 'smooth' as const,
    width: 0,
    lineCap: 'round' as const,
  },
  fill: {
    type: 'gradient',
    gradient: {
      shade: 'dark',
      type: 'vertical',
      shadeIntensity: 0.3,
      gradientToColors: ['rgba(0,0,0,0)'],
      inverseColors: false,
      opacityFrom: 0.8,
      opacityTo: 0.1,
      stops: [0, 100],
    },
  },
  dataLabels: {
    enabled: false,
  },
  plotOptions: {
    bar: {
      horizontal: false,
      borderRadius: 2,
    },
  },
  xaxis: {
    labels: {
      show: false,
    },
    axisTicks: {
      show: false,
    },
    axisBorder: {
      show: false,
    },
  },
  yaxis: {
    labels: {
      show: false,
    },
    min: 0,
  },
  tooltip: {
    enabled: false,
  },
  legend: {
    show: false,
  },
  grid: {
    show: false,
    padding: {
      left: -10,
      right: 0,
      bottom: -15,
      top: -15,
    },
    column: {
      opacity: 0,
    },
    xaxis: {
      lines: {
        show: false,
      },
    },
  },
  states: {
    hover: {
      filter: {
        type: 'lighten',
      }
    },
    active: {
      allowMultipleDataPointsSelection: false,
      filter: {
        type: 'darken',
      }
    }
  },
  responsive: [
    {
      breakpoint: 768,
      options: {
        chart: {
          height: 24,
        },
      },
    },
  ],
} satisfies ApexOptions;
</script>

<style scoped lang="css">
/* Line chart specific adjustments */
.line-chart .apexcharts-svg {
  transform: translateY(3px);
}

/* Chart container optimizations */
.chart-container {
  @apply w-full h-full;
}

.chart-container :deep(.apexcharts-canvas) {
  @apply w-full h-full;
}

/* Responsive chart heights */
@media (max-width: 640px) {
  .chart-container :deep(.apexcharts-svg) {
    height: 24px !important;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .chart-container :deep(.apexcharts-svg) {
    height: 32px !important;
  }
}

@media (min-width: 769px) {
  .chart-container :deep(.apexcharts-svg) {
    height: 40px !important;
  }
}

/* Smooth transitions for chart type changes */
.chart-container :deep(.apexcharts-svg) {
  @apply transition-all duration-300 ease-in-out;
}

/* Enhanced visual effects */
.chart-container :deep(.apexcharts-series) {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .chart-container :deep(.apexcharts-series) {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }
}

/* Mobile-specific optimizations */
@media (max-width: 480px) {
  .chart-container :deep(.apexcharts-tooltip) {
    display: none !important;
  }

  .chart-container :deep(.apexcharts-svg) {
    height: 20px !important;
  }
}
</style>
