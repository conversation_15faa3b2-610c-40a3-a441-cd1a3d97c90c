<template>
  <div class="flex flex-col gap-2 md:flex-row md:items-center md:gap-4">
    <div class="flex items-center md:w-32 md:flex-shrink-0">
      <FormLabel :for="id">
        {{ label }}
      </FormLabel>
      <BaseTooltip v-if="description" :text="description">
        <IconsInfo class="size-4" />
      </BaseTooltip>
    </div>
    <BaseSwitch :id="id" v-model="data" />
  </div>
</template>

<script lang="ts" setup>
defineProps<{ id: string; label: string; description?: string }>();
const data = defineModel<boolean>();
</script>
