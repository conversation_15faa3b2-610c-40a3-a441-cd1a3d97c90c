<template>
  <div class="flex items-center">
    <div class="flex items-center md:w-32 md:flex-shrink-0">
      <FormLabel :for="id">
        {{ label }}
      </FormLabel>
      <BaseTooltip v-if="description" :text="description">
        <IconsInfo class="size-4" />
      </BaseTooltip>
    </div>
    <BaseInput :id="id" v-model.number="data" :name="id" type="number" class="md:flex-1" />
  </div>
</template>

<script lang="ts" setup>
defineProps<{ id: string; label: string; description?: string }>();

const data = defineModel<number>();
</script>
