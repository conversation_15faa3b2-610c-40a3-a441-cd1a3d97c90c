<template>
  <div class="flex flex-col gap-2 md:flex-row md:items-center md:gap-4">
    <div class="flex items-center md:w-32 md:flex-shrink-0">
      <FormLabel :for="id">
        {{ label }}
      </FormLabel>
      <BaseTooltip v-if="description" :text="description">
        <IconsInfo class="size-4" />
      </BaseTooltip>
    </div>
    <BaseInput
      :id="id"
      :model-value="formattedDate"
      :name="id"
      type="date"
      max="9999-12-31"
      class="md:flex-1"
      @update:model-value="updateDate"
    />
  </div>
</template>

<script lang="ts" setup>
defineProps<{ id: string; label: string; description?: string }>();

const data = defineModel<string | null>();

const date = ref(data);

const formattedDate = computed(() => {
  return date.value ? date.value.split('T')[0] : '';
});

const updateDate = (value: unknown) => {
  if (typeof value !== 'string' && value !== null) {
    return;
  }

  const temp = value?.trim() ?? null;

  if (temp === '' || temp === null) {
    date.value = null;
  } else {
    date.value = new Date(temp).toISOString();
  }
};
</script>
