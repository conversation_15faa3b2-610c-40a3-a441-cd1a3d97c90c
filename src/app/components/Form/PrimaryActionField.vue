<template>
  <input
    :value="label"
    :type="type ?? 'button'"
    class="rounded-lg border-2 border-blue-600 bg-blue-600 px-3 py-1.5 text-sm text-white hover:border-blue-700 hover:bg-blue-700 focus:border-blue-800 focus:outline-0 focus:ring-0"
  />
</template>

<script lang="ts" setup>
import type { InputTypeHTMLAttribute } from 'vue';

defineProps<{
  label: string;
  type?: InputTypeHTMLAttribute;
}>();
</script>
