<template>
  <div class="flex flex-col gap-2 md:flex-row md:items-center md:gap-4">
    <div class="flex items-center md:w-32 md:flex-shrink-0">
      <FormLabel :for="id">
        {{ label }}
      </FormLabel>
      <BaseTooltip v-if="description" :text="description">
        <IconsInfo class="size-4" />
      </BaseTooltip>
    </div>
    <BaseInput
      :id="id"
      v-model.trim="data"
      :name="id"
      type="text"
      :autocomplete="autocomplete"
      :disabled="disabled"
      class="md:flex-1"
    />
  </div>
</template>

<script lang="ts" setup>
defineProps<{
  id: string;
  label: string;
  description?: string;
  autocomplete?: string;
  disabled?: boolean;
}>();

const data = defineModel<string>();
</script>
