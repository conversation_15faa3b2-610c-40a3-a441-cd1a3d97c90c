<template>
  <input
    :value="label"
    :type="type ?? 'button'"
    class="rounded-lg border-2 border-gray-300 px-3 py-1.5 text-sm text-gray-700 hover:border-blue-600 hover:bg-blue-600 hover:text-white focus:border-blue-800 focus:outline-0 focus:ring-0 dark:border-neutral-600 dark:bg-neutral-700 dark:text-neutral-200 dark:hover:border-blue-500 dark:hover:bg-blue-500"
  />
</template>

<script lang="ts" setup>
import type { InputTypeHTMLAttribute } from 'vue';

defineProps<{
  label: string;
  type?: InputTypeHTMLAttribute;
}>();
</script>
