<template>
  <BaseDialog :trigger-class="triggerClass">
    <template #trigger><slot /></template>
    <template #title>{{ $t('admin.config.suggest') }}</template>
    <template #description>
      <div class="space-y-3">
        <p class="text-sm text-gray-600 dark:text-neutral-400">{{ $t('admin.config.suggestDesc') }}</p>
        <div v-if="!data" class="flex items-center justify-center py-4">
          <IconsLoading class="h-4 w-4 animate-spin text-blue-600" />
          <span class="ml-2 text-sm">{{ $t('general.loading') }}</span>
        </div>
        <BaseSelect v-else v-model="selected" :options="data" />
      </div>
    </template>
    <template #actions>
      <DialogClose as-child>
        <BaseSecondaryButton>{{ $t('dialog.cancel') }}</BaseSecondaryButton>
      </DialogClose>
      <DialogClose as-child>
        <BasePrimaryButton @click="$emit('change', selected)">
          {{ $t('dialog.change') }}
        </BasePrimaryButton>
      </DialogClose>
    </template>
  </BaseDialog>
</template>

<script lang="ts" setup>
defineEmits(['change']);
const props = defineProps<{
  triggerClass?: string;
  url: '/api/admin/ip-info' | '/api/setup/4';
}>();

const { data } = useFetch(props.url, {
  method: 'get',
});

const selected = ref<string>();
</script>
