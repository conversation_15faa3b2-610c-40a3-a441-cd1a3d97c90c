export const UI_CHART_TYPES = [
  { type: undefined, strokeWidth: 0 },
  { type: 'line', strokeWidth: 2.5 },
  { type: 'area', strokeWidth: 0 },
  { type: 'bar', strokeWidth: 0 },
  { type: 'spline', strokeWidth: 2 },
] as const;

export const UI_CHART_PROPS = {
  line: { strokeWidth: 2.5 },
  area: { strokeWidth: 0 },
  bar: { strokeWidth: 0 },
  spline: { strokeWidth: 2 },
} as const;

// Modern blue-themed color scheme with enhanced gradients
export const CHART_COLORS = {
  rx: {
    light: 'rgba(59, 130, 246, 0.8)', // Blue-500 with opacity
    dark: 'rgba(96, 165, 250, 0.9)'   // Blue-400 with opacity
  },
  tx: {
    light: 'rgba(16, 185, 129, 0.8)', // Emerald-500 with opacity
    dark: 'rgba(52, 211, 153, 0.9)'   // Emerald-400 with opacity
  },
  gradient: {
    light: {
      rx: ['rgba(59, 130, 246, 0.4)', 'rgba(59, 130, 246, 0.05)'], // Blue gradient
      tx: ['rgba(16, 185, 129, 0.4)', 'rgba(16, 185, 129, 0.05)']  // Emerald gradient
    },
    dark: {
      rx: ['rgba(96, 165, 250, 0.5)', 'rgba(96, 165, 250, 0.08)'], // Blue gradient
      tx: ['rgba(52, 211, 153, 0.5)', 'rgba(52, 211, 153, 0.08)']  // Emerald gradient
    }
  },
  // Additional colors for enhanced visual variety
  accent: {
    light: 'rgba(139, 92, 246, 0.8)', // Violet-500
    dark: 'rgba(167, 139, 250, 0.9)'  // Violet-400
  }
};
