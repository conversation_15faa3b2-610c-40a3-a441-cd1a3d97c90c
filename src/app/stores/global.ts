export const useGlobalStore = defineStore('Global', () => {
  const { data: information } = useFetch('/api/information', {
    method: 'get',
  });

  const sortClient = ref(true); // Sort clients by name, true = asc, false = desc

  const uiShowCharts = useCookie<boolean>('uiShowCharts', {
    default: () => false,
    maxAge: 365 * 24 * 60 * 60,
  });

  function toggleCharts() {
    uiShowCharts.value = !uiShowCharts.value;
  }

  const uiChartType = useCookie<'area' | 'bar' | 'line' | 'spline'>('uiChartType', {
    default: () => 'area',
    maxAge: 365 * 24 * 60 * 60,
  });

  // Sidebar state management
  const sidebarCollapsed = useCookie<boolean>('sidebarCollapsed', {
    default: () => false,
    maxAge: 365 * 24 * 60 * 60,
  });

  function toggleSidebar() {
    sidebarCollapsed.value = !sidebarCollapsed.value;
  }

  function setSidebarCollapsed(collapsed: boolean) {
    sidebarCollapsed.value = collapsed;
  }

  return {
    sortClient,
    information,
    uiShowCharts,
    toggleCharts,
    uiChartType,
    sidebarCollapsed,
    toggleSidebar,
    setSidebarCollapsed,
  };
});
