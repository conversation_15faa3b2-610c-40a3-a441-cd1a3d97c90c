{"pages": {"me": "Ko<PERSON>", "clients": "Clients", "admin": {"panel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "general": "Allgemein", "config": "Konfiguration", "interface": "Oberfläche", "hooks": "<PERSON>s"}}, "user": {"email": "Email"}, "me": {"currentPassword": "Aktuelles Passwort", "enable2fa": "Zwei-Faktor-Authentifizierunng aktivieren", "enable2faDesc": "Scannen Sie den QR-Code mit ihrer Authentifizierungs-App oder geben Sie den Schlüssel manuell ein.", "2faKey": "TOTP-Schlüssel", "2faCodeDesc": "Geben Sie den Code aus Ihrer Authentifizierungs-App ein.", "disable2fa": "Zwei-Faktor-Authentifizierung deaktivieren", "disable2faDesc": "Geben Sie Ihr Passwort ein, um die Zwei-Faktor-Authentifizierung zu deaktivieren."}, "general": {"name": "Name", "username": "<PERSON><PERSON><PERSON><PERSON>", "password": "Passwort", "newPassword": "Neues Passwort", "updatePassword": "Passwort aktualisieren", "mtu": "MTU", "allowedIps": "Erlaubte IP-Adressen", "dns": "DNS", "persistentKeepalive": "Dauerhaftes Keepalive", "logout": "Abmelden", "continue": "<PERSON><PERSON>", "host": "Host", "port": "Port", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "confirmPassword": "Passwort bestätigen", "loading": "Laden...", "2fa": "Zwei-Faktor-Authentifizierung", "2faCode": "TOTP-Code", "refresh": "Aktualisieren"}, "setup": {"welcome": "Willkommen zur Ersteinrichtung von wg-easy", "welcomeDesc": "Das ist der einfachste Weg, um Wireguard auf jedem Linux-Server zu installieren und zu betreiben.", "existingSetup": "Haben Si<PERSON> eine bestehende Einrichtung?", "createAdminDesc": "Bitte geben Si<PERSON> zu<PERSON>t einen Admin-Benutzernamen sowie ein starkes, sicheres Passwort ein. Diese Anmeldedaten benötigen Sie, um sich im Admin-Panel anzumelden.", "setupConfigDesc": "Bitte geben Sie die Host- und Portinformationen ein. Diese werden für die Client-Konfiguration verwendet, wenn <PERSON><PERSON> WireGuard auf Ihren Geräten einrichten.", "setupMigrationDesc": "Bitte halten Sie die Sicherungsdatei bereit, wenn Sie Ihre Daten von I<PERSON> vorherigen wg-easy Version auf ihre neue Einrichtung migrieren möchten.", "upload": "Hochladen", "migration": "Sicherung wiederherstellen:", "createAccount": "<PERSON><PERSON> er<PERSON>", "successful": "Einrichtung erfolgreich", "hostDesc": "Öffentlicher Hostname mit dem sich die Clients verbinden", "portDesc": "Öffentlicher UDP-Port an dem sich die Clients verbinden und auf dem Wireguard läuft"}, "update": {"updateAvailable": "Es ist ein neue Aktualisierung verfügbar!", "update": "Aktualisieren"}, "theme": {"dark": "<PERSON><PERSON><PERSON>a", "light": "<PERSON><PERSON>", "system": "System-Thema"}, "layout": {"toggleCharts": "Statistiken ein-/ausblenden", "donate": "<PERSON><PERSON><PERSON>"}, "login": {"signIn": "Anmelden", "welcome": "Willkommen zurück", "signInToAccount": "Melden Sie sich in Ihrem Konto an, um fortzufahren", "secureConnection": "<PERSON><PERSON><PERSON> Verbindung", "rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "rememberMeDesc": "Bleiben Sie auch nach dem Schließen des Browsers angemeldet", "insecure": "<PERSON>e können sich nicht über eine unsichere Verbindung anmelden. Bitte benutzen Sie HTTPS.", "2faRequired": "Zwei-Faktor-Authentifizierung wird benötigt", "2faWrong": "Zwei-Faktor-Authentifizierung ist fehlgeschlagen"}, "client": {"empty": "<PERSON>s gibt noch keine Clients.", "newShort": "<PERSON>eu", "sort": "<PERSON><PERSON><PERSON><PERSON>", "create": "Client erstellen", "created": "Client wurde erstellt", "new": "Neuer client", "name": "Name", "expireDate": "Ablaufdatum", "expireDateDesc": "<PERSON><PERSON>, an dem der Client deaktiviert wird. <PERSON><PERSON>, damit dies nie passiert.", "deleteClient": "Client löschen", "deleteDialog1": "Sind <PERSON> sic<PERSON>, dass Sie diesen Client löschen wollen", "deleteDialog2": "Diese Aktion kann nicht rückgängig gemacht werden.", "enabled": "Aktiviert", "address": "<PERSON><PERSON><PERSON>", "serverAllowedIps": "serverseitig erlaubte IP-Adressen", "otlDesc": "Einen kurzen Einmal-Link erzeugen", "permanent": "<PERSON><PERSON><PERSON>", "createdOn": "Angelegt am ", "lastSeen": "Zuletzt verbunden am ", "totalDownload": "Gesamt-Download: ", "totalUpload": "Gesamt-Upload: ", "newClient": "Neuer Client", "disableClient": "Client deaktivieren", "enableClient": "Client aktivieren", "noPrivKey": "Dieser Client hat keinen bekannten privaten Schlüssel, we<PERSON><PERSON><PERSON> keine Konfiguration angelegt werden kann.", "showQR": "QR-Code anzeigen", "downloadConfig": "Konfiguration herunt<PERSON><PERSON>n", "allowedIpsDesc": "Welche IP-Adressen durch das VPN geleitet werden (überschreibt die globale Konfiguration)", "serverAllowedIpsDesc": "Welche IP-Adressen der Server zum Client leiten wird", "mtuDesc": "Setzt die maximale Übertragungsgröße (Paketgröße) für den VPN-Tunnel", "persistentKeepaliveDesc": "Legt das Intervall (in Sekunden) für Keepalive-Pakete fest. 0 deaktiviert es", "hooks": "<PERSON>s", "hooksDescription": "<PERSON>s funktionieren nur mit wg-quick", "hooksLeaveEmpty": "Nur für wg-quick. <PERSON><PERSON> leer lassen", "dnsDesc": "DNS-Server, den die Clients benutzen (überschreibt die globale Konfiguration)"}, "dialog": {"change": "Ändern", "cancel": "Abbrechen", "create": "<PERSON><PERSON><PERSON><PERSON>"}, "toast": {"success": "Erfolg", "saved": "Gespe<PERSON>rt", "error": "<PERSON><PERSON>"}, "form": {"actions": "Aktionen", "save": "Speichern", "revert": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "sectionGeneral": "Allgemein", "sectionAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noItems": "<PERSON><PERSON>", "nullNoItems": "<PERSON><PERSON> Einträge. Die globale Konfiguration wird benutzt", "add": "Hinzufügen"}, "admin": {"general": {"title": "Allgemeine Einstellungen", "sessionTimeout": "Sitzungszeitüberschreitung", "sessionTimeoutDesc": "Sitzungsdauer für \"Angemeldet bleiben\" (Sekunden)", "metrics": "Statistiken", "metricsPassword": "Passwort", "metricsPasswordDesc": "Bearer-Passwort für den Statistik-Endpunkt (Passwort oder Argon2-Hash)", "json": "JSON", "jsonDesc": "Pfad zu den Statistiken als JSON", "prometheus": "Prometheus", "prometheusDesc": "Pfad zu den Prometheus-Statistiken"}, "config": {"title": "Netzwerkkonfiguration", "connection": "Verbindung", "hostDesc": "Öffentlicher Hostname mit dem sich die Clients verbinden (überschreibt die Konfiguration)", "portDesc": "Öffentlicher UDP-Port an dem sich die Clients verbinden (überschreibt die Konfiguration, vermutlich wollen Sie ebenfalls den Port der Weboberfläche ändern)", "allowedIpsDesc": "Erlaubte IP-Adressen, die die Clients nutzen werden (Globale Konfiguration)", "dnsDesc": "DNS-Server, den die Clients nutzen werden (Globale Konfiguration)", "mtuDesc": "MTU, den die Clients benutzen werden (nur für neue Clients)", "persistentKeepaliveDesc": "Intervall in Sekunden, in dem Keepalive-Packete an den Server gesendet werden. 0 = deaktiviert (nur für neue Clients)", "suggest": "Vorschlagen", "suggestDesc": "Wählen Sie eine IP-Adresse oder einen Hostnamen für das Host-Feld aus"}, "interface": {"title": "Schnittstelleneinstellungen", "cidrSuccess": "CIDR wurde geändert", "device": "G<PERSON><PERSON>", "deviceDesc": "Ethernet-Ger<PERSON>, durch das der Wireguard-Datenverkehr geleitet werden soll", "mtuDesc": "MTU, den <PERSON><PERSON> ben<PERSON> wird", "portDesc": "UDP Port, auf dem WireGuard lauschen wird (Sie wollen wahrscheinlich auch den Config-Port ändern)", "changeCidr": "CIDR ändern", "restart": "Interface neu starten", "restartDesc": "<PERSON>-Interface neu starten", "restartWarn": "Sind <PERSON> sic<PERSON>, dass Sie das Interface neu starten wollen? Dies wird die Verbindungen aller Clients trennen.", "restartSuccess": "Interface neu gestartet"}, "introText": "Willkommen in der Admin-Konsole.\n\nHier können Sie die allgemeinen Einstellungen, die Konfiguration, die Schnittstelleneinstellungen und die Hooks verwalten.\n\nBeginnen Sie, indem Sie einen der Bereiche in der Seitenleiste auswählen."}, "zod": {"generic": {"required": "{0} ist erforderlich", "validNumber": "{0} muss eine <PERSON> sein", "validString": "{0} muss eine Zeichenkette sein", "validBoolean": "{0} muss ein Wahrheitswert sein", "validArray": "{0} muss eine <PERSON> sein", "stringMin": "{0} muss <PERSON>ns {1} <PERSON><PERSON><PERSON> lang sein", "numberMin": "{0} muss mindestens {1} sein"}, "client": {"id": "Client-ID", "name": "Name", "expiresAt": "Läuft ab am", "address4": "IPv4-<PERSON><PERSON><PERSON>", "address6": "IPv6-<PERSON><PERSON><PERSON>", "serverAllowedIps": "serverseitig erlaubte IP-Adressen"}, "user": {"username": "<PERSON><PERSON><PERSON><PERSON>", "password": "Passwort", "remember": "<PERSON><PERSON><PERSON>", "name": "Name", "email": "Email", "emailInvalid": "Die Email-<PERSON>resse muss valide sein", "passwordMatch": "Die Passwörter müssen übereinstimmen", "totpEnable": "TOTP aktivieren", "totpEnableTrue": "\"TOTP aktivieren\" muss ausgewählt sein", "totpCode": "TOTP-Code"}, "userConfig": {"host": "Host"}, "general": {"sessionTimeout": "Sitzungszeitüberschreitung", "metricsEnabled": "Statistiken", "metricsPassword": "Passwort für Statistiken"}, "interface": {"cidr": "CIDR", "device": "<PERSON><PERSON><PERSON><PERSON>", "cidrValid": "CIDR muss valide sein"}, "otl": "Einmal-Link", "stringMalformed": "Zeichenkette ist fehlerhaft", "body": "Body muss ein gültiges Objekt sein", "hook": "Hook", "enabled": "Aktiviert", "mtu": "MTU", "port": "Port", "persistentKeepalive": "Dauerhaftes Keepalive", "address": "IP-Adresse", "dns": "DNS", "allowedIps": "Erlaubte IP-Adressen", "file": "<PERSON><PERSON>"}, "hooks": {"title": "Hook-<PERSON><PERSON><PERSON><PERSON>", "preUp": "PreUp", "preUpDesc": "<PERSON><PERSON><PERSON><PERSON>, die vor dem Aktivieren der Schnittstelle ausgeführt werden", "postUp": "PostUp", "postUpDesc": "<PERSON><PERSON><PERSON><PERSON>, die nach dem Aktivieren der Schnittstelle ausgeführt werden", "preDown": "PreDown", "preDownDesc": "<PERSON><PERSON><PERSON><PERSON>, die vor dem Deaktivieren der Schnittstelle ausgeführt werden", "postDown": "PostDown", "postDownDesc": "<PERSON><PERSON><PERSON><PERSON>, die nach dem Deaktivieren der Schnittstelle ausgeführt werden"}}