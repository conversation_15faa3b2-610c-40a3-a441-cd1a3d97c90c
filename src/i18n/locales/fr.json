{"pages": {"me": "<PERSON><PERSON><PERSON>", "clients": "Clients", "admin": {"panel": "Panel Admin", "general": "Général", "config": "Config", "interface": "Interface", "hooks": "<PERSON>s"}}, "user": {"email": "E-Mail"}, "me": {"currentPassword": "Mot de passe actuel", "enable2fa": "Activer l'authentification à double facteur", "enable2faDesc": "Scannez le code QR avec votre application d'authentification ou saisissez la clé manuellement.", "2faKey": "Clé TOTP", "2faCodeDesc": "Saisissez le code de votre application d'authentification.", "disable2fa": "Désactiver l'authentification à double facteur", "disable2faDesc": "Saisissez votre mot de passe pour désactiver l'authentification à double facteur"}, "general": {"name": "Nom", "username": "Nom d'utilisateur", "password": "Mot de passe", "newPassword": "Nouveau mot de passe", "updatePassword": "Mettre à jour le mot de passe", "mtu": "MTU", "allowedIps": "IPs autorisées", "dns": "DNS", "persistentKeepalive": "Keepalive persistant", "logout": "Déconnexion", "continue": "<PERSON><PERSON><PERSON>", "host": "<PERSON><PERSON><PERSON>", "port": "Port", "yes": "O<PERSON>", "no": "Non", "confirmPassword": "Confirmer le mot de passe", "loading": "Chargement...", "2fa": "Authentification à double facteur", "2faCode": "Code TOTP", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setup": {"welcome": "Bienvenue dans votre première installation de wg-easy", "welcomeDesc": "Vous avez trouvé la façon la plus simple d'installer et de gérer WireGuard sur n'importe quel hôte Linux.", "existingSetup": "<PERSON><PERSON><PERSON>vous une installation existante ?", "createAdminDesc": "Veuillez d'abord saisir un nom d'utilisateur administrateur et un mot de passe sécurisé. Ces informations seront utilisées pour vous connecter à votre panel d'administration.", "setupConfigDesc": "Veuillez saisir les informations relatives à l'hôte et au port. Ceci sera utilisé pour la configuration du client lors de la mise en place de WireGuard sur les appareils.", "setupMigrationDesc": "Veuillez fournir le fichier de sauvegarde si vous souhaitez migrer vos données de la version précédente de wg-easy vers votre nouvelle installation.", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "migration": "Restaurer la sauvegarde:", "createAccount": "<PERSON><PERSON><PERSON> un compte", "successful": "Installation réussie", "hostDesc": "Nom d'hôte public auquel les clients se connecteront", "portDesc": "Port UDP public auquel les clients se connecteront et sur lequel WireGuard écoutera"}, "update": {"updateAvailable": "Une mise à jour est disponible!", "update": "Mise à jour"}, "theme": {"dark": "Thème sombre", "light": "Thème clair", "system": "Thème système"}, "layout": {"toggleCharts": "Afficher/masquer les graphiques", "donate": "Donation"}, "login": {"signIn": "Se connecter", "welcome": "Bon retour", "signInToAccount": "Connectez-vous à votre compte pour continuer", "secureConnection": "Connexion sécurisée", "rememberMe": "Se souvenir de moi", "rememberMeDesc": "Rester connecté après avoir fermé le navigateur", "insecure": "Vous ne pouvez pas vous connecter avec une connexion non sécurisée. Utilisez HTTPS.", "2faRequired": "Une authentification à double facteur est requise", "2faWrong": "L'authentification à double facteur est incorrecte"}, "client": {"empty": "Il n'y a pas encore de clients.", "newShort": "Nouveau", "sort": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> un client", "created": "Client c<PERSON>", "new": "Nouveau client", "name": "Nom", "expireDate": "Date d'expiration", "expireDateDesc": "Date à laquelle le client sera désactivé. Vide pour permanent", "deleteClient": "Supprimer le client", "deleteDialog1": "Êtes-vous sûr de vouloir supprimer", "deleteDialog2": "Cette action ne peut être annulée.", "enabled": "Activé", "address": "<PERSON><PERSON><PERSON>", "serverAllowedIps": "Serveur IPs autorisées", "otlDesc": "Générer un lien court et unique", "permanent": "Permanent", "createdOn": "<PERSON><PERSON><PERSON> ", "lastSeen": "<PERSON><PERSON><PERSON> visite le ", "totalDownload": "Téléchargement total: ", "totalUpload": "Téléversement total: ", "newClient": "Nouveau client", "disableClient": "Désactiver le client", "enableClient": "Activer le client", "noPrivKey": "Ce client n'a pas de clé privée connue. Impossible de créer une configuration.", "showQR": "Afficher le code QR", "downloadConfig": "Télécharger la configuration", "allowedIpsDesc": "Quelles IPs seront acheminées par le VPN (remplace la configuration globale)", "serverAllowedIpsDesc": "Les IPs que le serveur acheminera vers le client", "mtuDesc": "Définit le nombre maximum d'unités de transmission (taille des paquets) pour le tunnel VPN.", "persistentKeepaliveDesc": "Définit l'intervalle (en secondes) pour les paquets keep-alive. 0 le désactive", "hooks": "<PERSON>s", "hooksDescription": "Les hooks ne fonctionnent qu'avec wg-quick", "hooksLeaveEmpty": "Uniquement pour wg-quick. Sinon, laissez-le vide", "dnsDesc": "Serveur DNS que les clients utiliseront (remplace la configuration globale)"}, "dialog": {"change": "Modifier", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>"}, "toast": {"success": "Réussite", "saved": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>"}, "form": {"actions": "Actions", "save": "<PERSON><PERSON><PERSON><PERSON>", "revert": "Revenir en arrière", "sectionGeneral": "Général", "sectionAdvanced": "<PERSON><PERSON><PERSON>", "noItems": "Aucun élément", "nullNoItems": "Aucun élément. Utilisation de la configuration globale", "add": "Ajouter"}, "admin": {"general": {"title": "Paramètres généraux", "sessionTimeout": "<PERSON><PERSON><PERSON> d'expiration de la session", "sessionTimeoutDesc": "Du<PERSON>e de la session pour Se souvenir de moi (secondes)", "metrics": "Métriques", "metricsPassword": "Mot de passe", "metricsPasswordDesc": "Mot de passe Bearer pour le endpoint des métriques (mot de passe ou argon2 hash)", "json": "JSON", "jsonDesc": "Acheminement pour les métriques au format JSON", "prometheus": "Prometheus", "prometheusDesc": "Acheminement pour les métriques de Prometheus"}, "config": {"title": "Configuration r<PERSON><PERSON>", "connection": "Connexion", "hostDesc": "Nom d'hôte public auquel les clients se connecteront (invalide la configuration)", "portDesc": "Port UDP public auquel les clients se connecteront (invalide la configuration, vous souhaiterez probablement modifier le port d'interface également)", "allowedIpsDesc": "IPs autorisées que les clients utiliseront (configuration globale)", "dnsDesc": "Serveur DNS que les clients utiliseront (configuration globale)", "mtuDesc": "MTU que les clients utiliseront (uniquement pour les nouveaux clients)", "persistentKeepaliveDesc": "Intervalle en secondes keepalives du serveur. 0 = désactivé (uniquement pour les nouveaux clients)", "suggest": "<PERSON><PERSON><PERSON><PERSON>", "suggestDesc": "Choisissez une adresse IP ou un nom d'hôte pour le champ Hôte."}, "interface": {"title": "Paramètres d'interface", "cidrSuccess": "CIDR modifié", "device": "Périphé<PERSON><PERSON>", "deviceDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dans lequel le trafic wireguard sera transmis.", "mtuDesc": "MTU que WireGuard utilisera", "portDesc": "Port UDP sur lequel WireGuard é<PERSON>a (vous souhaiterez probablement changer le port de configuration aussi)", "changeCidr": "Modifier CIDR", "restart": "Redémarrer l'interface", "restartDesc": "Redémarre l'interface WireGuard", "restartWarn": "Êtes-vous sûr de redémarrer l'interface ? Cela déconnectera tous les clients.", "restartSuccess": "Interface red<PERSON><PERSON><PERSON><PERSON>"}, "introText": "Bienvenue dans le panel d'administration.\n\nV<PERSON> pouvez y gérer les paramètres généraux, la configuration, les paramètres de l'interface et les hooks.\n\nCommencez par choisir l'une des sections de la barre latérale."}, "zod": {"generic": {"required": "{0} est requis", "validNumber": "{0} doit être un nombre valide", "validString": "{0} doit être une chaîne de caractères valide", "validBoolean": "{0} doit être une variable valide", "validArray": "{0} doit être un tableau valide", "stringMin": "{0} doit être d'au moins {1} Caractère", "numberMin": "{0} doit être d'au moins {1}"}, "client": {"id": "Client ID", "name": "Nom", "expiresAt": "Expire le", "address4": "Adresse IPv4", "address6": "Adresse IPv6", "serverAllowedIps": "Serveur IPs autorisées"}, "user": {"username": "Nom d'utilisateur", "password": "Mot de passe", "remember": "Se souvenir", "name": "Nom", "email": "Email", "emailInvalid": "L'email doit être valide", "passwordMatch": "Les mots de passe doivent correspondre", "totpEnable": "Activer TOTP", "totpEnableTrue": "Activer TOTP doit être activé", "totpCode": "Code TOTP"}, "userConfig": {"host": "<PERSON><PERSON><PERSON>"}, "general": {"sessionTimeout": "<PERSON><PERSON><PERSON> d'expiration de la session", "metricsEnabled": "Métriques", "metricsPassword": "Mot de passe métriques"}, "interface": {"cidr": "CIDR", "device": "Périphé<PERSON><PERSON>", "cidrValid": "CIDR doit être valide"}, "otl": "Lien unique", "stringMalformed": "La chaîne est malformée", "body": "Le corps doit être un objet valide", "hook": "Hook", "enabled": "Activé", "mtu": "MTU", "port": "Port", "persistentKeepalive": "Keepalive persistant", "address": "Adresse IP", "dns": "DNS", "allowedIps": "IPs autorisées", "file": "<PERSON><PERSON><PERSON>"}, "hooks": {"title": "Scripts de hooks", "preUp": "PreUp", "preUpDesc": "Commandes à exécuter avant l'activation de l'interface", "postUp": "PostUp", "postUpDesc": "Commandes à exécuter après l'activation de l'interface", "preDown": "PreDown", "preDownDesc": "Commandes à exécuter avant la désactivation de l'interface", "postDown": "PostDown", "postDownDesc": "Commandes à exécuter après la désactivation de l'interface"}}