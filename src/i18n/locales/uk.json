{"pages": {"me": "Обліковий запис", "clients": "Клієнти", "admin": {"panel": "Панель адміністратора", "general": "Загальні налаштування", "config": "Конфігурація", "interface": "Інтерфейс", "hooks": "<PERSON>s"}}, "user": {"email": "Електронна пошта"}, "me": {"currentPassword": "Поточний пароль", "enable2fa": "Увімкнути двофакторну автентифікацію", "enable2faDesc": "Відскануйте QR-код за допомогою програми автентифікації або введіть ключ вручну.", "2faKey": "<PERSON><PERSON>", "2faCodeDesc": "Введіть код з вашої програми автентифікатора.", "disable2fa": "Вимкнути двофакторну автентифікацію", "disable2faDesc": "Введіть свій пароль, щоб вимкнути двофакторну автентифікацію."}, "general": {"name": "Ім'я", "username": "Ім'я користувача", "password": "Пароль", "newPassword": "Новий пароль", "updatePassword": "Оновити пароль", "mtu": "MTU", "allowedIps": "Дозволені IP", "dns": "DNS", "persistentKeepalive": "Постійна підтримка активності", "logout": "Вийти", "continue": "Продовжити", "host": "Хо<PERSON>т", "port": "Порт", "yes": "Так", "no": "Ні", "confirmPassword": "Підтвердити пароль", "loading": "Завантаження...", "2fa": "Двофакто<PERSON>на автентифікація", "2faCode": "TOTP код", "refresh": "Оновити"}, "setup": {"welcome": "Ласкаво просимо до вашого першого налаштування wg-easy", "welcomeDesc": "Ви знайшли найпростіший спосіб встановити та керувати WireGuard на будь-якому хості Linux", "existingSetup": "Ви маєте існуючу конфігурацію?", "createAdminDesc": "Спочатку введіть ім'я адміністратора та надійний пароль. Ці дані використовуватимуться для входу в панель адміністратора.", "setupConfigDesc": "Введіть інформацію про хост і порт. Вона буде використана у конфігурації клієнта при налаштуванн<PERSON> WireGuard на їх пристроях.", "setupMigrationDesc": "Будь ласка, надайте файл резервної копії, якщо ви хочете перенести дані з попередньої версії wg-easy до нової конфігурації.", "upload": "Завант<PERSON><PERSON><PERSON>ти", "migration": "Відновити резервну копію:", "createAccount": "Створити обліковий запис", "successful": "Налаштування успішне", "hostDesc": "Публічне ім'я хоста для підключення клієнтів", "portDesc": "Публічний UDP порт, на якому клієнти підключаються і який слухає WireGuard"}, "update": {"updateAvailable": "Доступне оновлення!", "update": "Оновлення"}, "theme": {"dark": "Темна тема", "light": "Світла тема", "system": "Системна тема"}, "layout": {"toggleCharts": "Показати/приховати діаграми", "donate": "Пожертвувати"}, "login": {"signIn": "Увійти", "welcome": "Ласкаво просимо назад", "signInToAccount": "Увійдіть до свого облікового запису, щоб продовжити", "secureConnection": "Безпечне з'єднання", "rememberMe": "Запам'ятай мене", "rememberMeDesc": "Залишатися в системі після закриття браузера", "insecure": "Ви не можете увійти через незахищене з'єднання. Використовуйте HTTPS.", "2faRequired": "Потрібна двофакторна автентифікація", "2faWrong": "Неправильний код двофакторної автентифікації"}, "client": {"empty": "Клієнтів поки немає.", "newShort": "Новий", "sort": "Сортувати", "create": "Створити клієнта", "created": "Клієнт створено", "new": "Новий клієнт", "name": "Ім'я", "expireDate": "Термін дії", "expireDateDesc": "Дата, коли клієнт буде відключений. Порожнє для постійного користування", "deleteClient": "Видалити клієнта", "deleteDialog1": "Ви впевнені, що бажаєте видалити", "deleteDialog2": "Цю дію неможливо скасувати.", "enabled": "Увімкнено", "address": "Адреса", "serverAllowedIps": "Дозволені IP-адреси сервера", "otlDesc": "Створити коротке одноразове посилання", "permanent": "Постійний", "createdOn": "Створено ", "lastSeen": "Останнє підключення в ", "totalDownload": "Всього завантажено: ", "totalUpload": "Всього відправлено: ", "newClient": "Новий клієнт", "disableClient": "Вимкнути клієнта", "enableClient": "Увімкнути клієнта", "noPrivKey": "У цього клієнта відсутній приватний ключ. Неможливо створити конфігурацію.", "showQR": "Показати QR-код", "downloadConfig": "Заванта<PERSON>ити конфігурацію", "allowedIpsDesc": "Які IP-адреси будуть маршрутизовані через VPN (перевизначає глобальну конфігурацію)", "serverAllowedIpsDesc": "Які IP-адреси сервер буде перенаправляти до клієнта", "mtuDesc": "Встановлює максимальний розмір пакета (одиницю передачі) для VPN-тунелю", "persistentKeepaliveDesc": "Встановлює інтервал (у секундах) для пакетів keep-alive. 0 вимикає його", "hooks": "<PERSON>s", "hooksDescription": "Hooks працюють лише з wg-quick", "hooksLeaveEmpty": "Тільки для wg-quick. Інакше залиште порожнім", "dnsDesc": "DNS сервер, який використовуватимуть клієнти (перевизначає глобальну конфігурацію)"}, "dialog": {"change": "Змінити", "cancel": "Скасувати", "create": "Створити"}, "toast": {"success": "Успіх", "saved": "Збережено", "error": "Помилка"}, "form": {"actions": "Дії", "save": "Зберегти", "revert": "Повернути", "sectionGeneral": "Загальні", "sectionAdvanced": "Додатково", "noItems": "Немає елементів", "nullNoItems": "Немає елементів. Використовується глобальна конфігурація", "add": "Додати"}, "admin": {"general": {"title": "Загальні налаштування", "sessionTimeout": "Час очікування сеансу", "sessionTimeoutDesc": "Тривалість сеансу для функції 'Запам'ятати мене' (секунди)", "metrics": "Метрики", "metricsPassword": "Пароль", "metricsPasswordDesc": "Пароль Bearer для точки доступу метрик (пароль або хеш argon2)", "json": "JSON", "jsonDesc": "Маршрут для метрик у форматі JSON", "prometheus": "Prometheus", "prometheusDesc": "Маршрут для метрики Prometheus"}, "config": {"title": "Мережева конфігурація", "connection": "З'єднання", "hostDesc": "Публічне ім'я хоста для підключення клієнтів (спрацьовує при зміні конфігурації)", "portDesc": "Публічний UDP порт для підключення клієнтів (спрацьовує при зміні конфігурації, можливо, варто змінити порт інтерфейсу теж)", "allowedIpsDesc": "Дозволені IP-адреси, які використовуватимуть клієнти (глобальна конфігурація)", "dnsDesc": "DNS-сервера, який використовуватимуть клієнти (глобальну конфігурацію)", "mtuDesc": "MTU, який використовуватимуть клієнти (лише для нових клієнтів)", "persistentKeepaliveDesc": "Інтервал у секундах для надсилання keepalive на сервер. 0 = вимкнено (лише для нових клієнтів)", "suggest": "Запропонувати", "suggestDesc": "Виберіть IP-адресу або ім'я хоста для поля 'Хост'"}, "interface": {"title": "Налаштування інтерфейсу", "cidrSuccess": "CIDR змінено", "device": "Прис<PERSON><PERSON><PERSON>й", "deviceDesc": "Ethernet-пристрій, через який має проходити трафік WireGuard", "mtuDesc": "MTU, яке використовуватиме WireGuard", "portDesc": "UDP порт, який слухати<PERSON><PERSON> Wire<PERSON>uard (ймовірно, варто змінити порт у конфігурації теж)", "changeCidr": "Змінити CIDR", "restart": "Перезавантажити інтерфейс", "restartDesc": "Перезавантажити інтерфейс WireGuard", "restartWarn": "Ви впевнені, що бажаєте перезавантажити інтерфейс? Це призведе до відключення всіх клієнтів.", "restartSuccess": "Інтерфейс перезавантажено"}, "introText": "Ласкаво просимо до панелі адміністратора.\n\nТут ви можете керувати загальними налаштуваннями, конфігурацією, налаштуваннями інтерфейсу та перехоплювачами.\n\nПочніть з вибору одного з розділів на боковій панелі."}, "zod": {"generic": {"required": "{0} обов'язковий", "validNumber": "{0} має бути дійсним числом", "validString": "{0} має бути дійсним рядком", "validBoolean": "{0} має бути дійсним логічним значенням", "validArray": "{0} має бути дійсним масивом", "stringMin": "{0} має містити щонайменше {1} символів", "numberMin": "{0} має бути щонайменше {1}"}, "client": {"id": "Ідентифікатор клієнта", "name": "Ім'я", "expiresAt": "Термін дії закінчується о", "address4": "IPv4-адреса", "address6": "IPv6-адреса", "serverAllowedIps": "Дозволені IP-адреси сервера"}, "user": {"username": "Ім'я користувача", "password": "Пароль", "remember": "Пам'ятати", "name": "Ім'я", "email": "Електронна пошта", "emailInvalid": "Email має бути дійсною", "passwordMatch": "Паролі мають збігатися", "totpEnable": "Увімкнути TOTP", "totpEnableTrue": "Увімкнення TOTP має бути true", "totpCode": "TOTP Код"}, "userConfig": {"host": "Хо<PERSON>т"}, "general": {"sessionTimeout": "Час очікування сеансу", "metricsEnabled": "Метрики", "metricsPassword": "Пароль метрик"}, "interface": {"cidr": "CIDR", "device": "Прис<PERSON><PERSON><PERSON>й", "cidrValid": "CIDR має бути дійсним"}, "otl": "Одноразове посилання", "stringMalformed": "Рядок має неправильний формат", "body": "Тіло має бути коректним об'єктом", "hook": "Hook", "enabled": "Увімкнено", "mtu": "MTU", "port": "Порт", "persistentKeepalive": "Постійна підтримка активності", "address": "IP-адреса", "dns": "DNS", "allowedIps": "Дозволені IP-адреси", "file": "<PERSON>а<PERSON><PERSON>"}, "hooks": {"title": "Hook-скрипти", "preUp": "PreUp", "preUpDesc": "Команди для виконання перед активацією інтерфейсу", "postUp": "PostUp", "postUpDesc": "Команди для виконання після активації інтерфейсу", "preDown": "PreDown", "preDownDesc": "Команди для виконання перед деактивацією інтерфейсу", "postDown": "PostDown", "postDownDesc": "Команди для виконання після деактивації інтерфейсу"}}