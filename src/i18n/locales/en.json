{"pages": {"me": "Account", "clients": "Clients", "admin": {"panel": "Admin Panel", "general": "General", "config": "Config", "interface": "Interface", "hooks": "<PERSON>s"}}, "user": {"email": "E-Mail"}, "me": {"currentPassword": "Current Password", "enable2fa": "Enable Two Factor Authentication", "enable2faDesc": "Scan the QR code with your authenticator app or enter the key manually.", "2faKey": "TOTP Key", "2faCodeDesc": "Enter the code from your authenticator app.", "disable2fa": "Disable Two Factor Authentication", "disable2faDesc": "Enter your password to disable Two Factor Authentication."}, "general": {"name": "Name", "username": "Username", "password": "Password", "newPassword": "New Password", "updatePassword": "Update Password", "mtu": "MTU", "allowedIps": "Allowed IPs", "dns": "DNS", "persistentKeepalive": "Persistent Keepalive", "logout": "Logout", "continue": "Continue", "host": "Host", "port": "Port", "yes": "Yes", "no": "No", "confirmPassword": "Confirm Password", "loading": "Loading...", "2fa": "Two Factor Authentication", "2faCode": "TOTP Code", "status": "Status", "description": "Description", "ipAddress": "IP Address", "actions": "Actions", "active": "Active", "inactive": "Inactive", "add": "Add", "refresh": "Refresh"}, "setup": {"welcome": "Welcome to your first setup of wg-easy", "welcomeDesc": "You have found the easiest way to install and manage WireGuard on any Linux host", "existingSetup": "Do you have an existing setup?", "createAdminDesc": "Please first enter an admin username and a strong secure password. This information will be used to log in to your administration panel.", "setupConfigDesc": "Please enter the host and port information. This will be used for the client configuration when setting up WireGuard on their devices.", "setupMigrationDesc": "Please provide the backup file if you want to migrate your data from your previous wg-easy version to your new setup.", "upload": "Upload", "migration": "Restore the backup:", "createAccount": "Create Account", "successful": "Setup successful", "hostDesc": "Public hostname clients will connect to", "portDesc": "Public UDP port clients will connect to and WireGuard will listen on"}, "update": {"updateAvailable": "There is an update available!", "update": "Update"}, "theme": {"dark": "Dark theme", "light": "Light theme", "system": "System theme"}, "layout": {"toggleCharts": "Show/hide Charts", "donate": "Donate"}, "login": {"signIn": "Sign In", "welcome": "Welcome back", "signInToAccount": "Sign in to your account to continue", "secureConnection": "Secure connection", "rememberMe": "Remember me", "rememberMeDesc": "Stay logged after closing the browser", "insecure": "You can't log in with an insecure connection. Use HTTPS.", "2faRequired": "Two Factor Authentication is required", "2faWrong": "Two Factor Authentication is wrong"}, "client": {"empty": "There are no clients yet.", "newShort": "New", "sort": "Sort", "create": "Create Client", "created": "Client created", "new": "New Client", "name": "Name", "expireDate": "Expire Date", "expireDateDesc": "Date the client will be disabled. Blank for permanent", "deleteClient": "Delete Client", "deleteDialog1": "Are you sure you want to delete", "deleteDialog2": "This action cannot be undone.", "enabled": "Enabled", "address": "Address", "serverAllowedIps": "Server Allowed IPs", "otlDesc": "Generate short one time link", "permanent": "Permanent", "createdOn": "Created on ", "lastSeen": "Last seen on ", "totalDownload": "Total Download: ", "totalUpload": "Total Upload: ", "newClient": "New Client", "disableClient": "Disable Client", "enableClient": "Enable Client", "edit": "Edit Client", "noPrivKey": "This client has no known private key. Cannot create Configuration.", "showQR": "Show QR Code", "downloadConfig": "Download Configuration", "allowedIpsDesc": "Which IPs will be routed through the VPN (overrides global config)", "serverAllowedIpsDesc": "Which IPs the server will route to the client", "mtuDesc": "Sets the maximum transmission unit (packet size) for the VPN tunnel", "persistentKeepaliveDesc": "Sets the interval (in seconds) for keep-alive packets. 0 disables it", "hooks": "<PERSON>s", "hooksDescription": "Hooks only work with wg-quick", "hooksLeaveEmpty": "Only for wg-quick. Otherwise, leave it empty", "dnsDesc": "DNS server clients will use (overrides global config)", "actions": "Actions", "equipments": "Equipments", "equipmentsDesc": "Manage equipment connected to this client", "noEquipments": "No equipment found", "addEquipmentDesc": "Add a new equipment device to this client", "editEquipment": "Edit Equipment", "editEquipmentDesc": "Modify equipment device information"}, "dialog": {"change": "Change", "cancel": "Cancel", "create": "Create", "close": "Close", "save": "Save"}, "toast": {"success": "Success", "saved": "Saved", "error": "Error"}, "form": {"actions": "Actions", "save": "Save", "revert": "<PERSON><PERSON>", "delete": "Delete", "sectionGeneral": "General", "sectionAdvanced": "Advanced", "noItems": "No items", "nullNoItems": "No items. Using global config", "add": "Add"}, "admin": {"general": {"title": "General Settings", "sessionTimeout": "Session Timeout", "sessionTimeoutDesc": "Session duration for Remember Me (seconds)", "metrics": "Metrics", "metricsPassword": "Password", "metricsPasswordDesc": "Bearer Password for the metrics endpoint (password or argon2 hash)", "json": "JSON", "jsonDesc": "Route for metrics in JSON format", "prometheus": "Prometheus", "prometheusDesc": "Route for Prometheus metrics"}, "config": {"title": "Network Configuration", "connection": "Connection", "hostDesc": "Public hostname clients will connect to (invalidates config)", "portDesc": "Public UDP port clients will connect to (invalidates config, you probably want to change Interface Port too)", "allowedIpsDesc": "Allowed IPs clients will use (global config)", "dnsDesc": "DNS server clients will use (global config)", "mtuDesc": "MTU clients will use (only for new clients)", "persistentKeepaliveDesc": "Interval in seconds to send keepalives to the server. 0 = disabled (only for new clients)", "suggest": "Suggest", "suggestDesc": "Choose a IP-Address or Hostname for the Host field"}, "interface": {"title": "Interface Settings", "cidrSuccess": "Changed CIDR", "device": "<PERSON><PERSON>", "deviceDesc": "Ethernet device the wireguard traffic should be forwarded through", "mtuDesc": "MTU WireGuard will use", "portDesc": "UDP Port WireGuard will listen on (you probably want to change Config Port too)", "changeCidr": "Change CIDR", "restart": "Restart Interface", "restartDesc": "Restart the WireGuard interface", "restartWarn": "Are you sure to restart the interface? This will disconnect all clients.", "restartSuccess": "Interface restarted"}, "introText": "Welcome to the admin panel.\n\nHere you can manage the general settings, the configuration, the interface settings and the hooks.\n\nStart by choosing one of the sections in the sidebar."}, "zod": {"generic": {"required": "{0} is required", "validNumber": "{0} must be a valid number", "validString": "{0} must be a valid string", "validBoolean": "{0} must be a valid boolean", "validArray": "{0} must be a valid array", "stringMin": "{0} must be at least {1} Character", "numberMin": "{0} must be at least {1}"}, "client": {"id": "Client ID", "name": "Name", "expiresAt": "Expires At", "address4": "IPv4 Address", "address6": "IPv6 Address", "serverAllowedIps": "Server Allowed IPs"}, "user": {"username": "Username", "password": "Password", "remember": "Remember", "name": "Name", "email": "Email", "emailInvalid": "Email must be a valid email", "passwordMatch": "Passwords must match", "totpEnable": "TOTP Enable", "totpEnableTrue": "TOTP Enable must be true", "totpCode": "TOTP Code"}, "userConfig": {"host": "Host"}, "general": {"sessionTimeout": "Session Timeout", "metricsEnabled": "Metrics", "metricsPassword": "Metrics Password"}, "interface": {"cidr": "CIDR", "device": "<PERSON><PERSON>", "cidrValid": "CIDR must be valid"}, "otl": "One Time link", "stringMalformed": "String is malformed", "body": "Body must be a valid object", "hook": "Hook", "enabled": "Enabled", "mtu": "MTU", "port": "Port", "persistentKeepalive": "Persistent Keepalive", "address": "IP Address", "dns": "DNS", "allowedIps": "Allowed IPs", "file": "File"}, "hooks": {"title": "<PERSON>", "preUp": "PreUp", "preUpDesc": "Commands to run before the interface is brought up", "postUp": "PostUp", "postUpDesc": "Commands to run after the interface is brought up", "preDown": "PreDown", "preDownDesc": "Commands to run before the interface is brought down", "postDown": "PostDown", "postDownDesc": "Commands to run after the interface is brought down"}}