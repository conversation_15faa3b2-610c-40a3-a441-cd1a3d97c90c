{"name": "wg-easy", "version": "15.1.0", "description": "The easiest way to run WireGuard VPN + Web-based Admin UI.", "private": true, "type": "module", "scripts": {"build": "nuxt build && pnpm cli:build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "format": "prettier . --write", "format:check": "prettier . --check", "typecheck": "nuxt typecheck", "check:all": "pnpm typecheck && pnpm lint && pnpm format:check && pnpm build", "db:generate": "drizzle-kit generate", "cli:build": "node cli/build.js", "cli:dev": "tsx cli/index.ts"}, "dependencies": {"@eschricht/nuxt-color-mode": "^1.1.5", "@heroicons/vue": "^2.2.0", "@libsql/client": "^0.15.9", "@nuxtjs/i18n": "^9.5.6", "@nuxtjs/tailwindcss": "^6.14.0", "@phc/format": "^1.0.0", "@pinia/nuxt": "^0.11.1", "@tailwindcss/forms": "^0.5.10", "apexcharts": "^4.7.0", "argon2": "^0.43.0", "cidr-tools": "^11.0.3", "citty": "^0.1.6", "consola": "^3.4.2", "crc-32": "^1.2.2", "debug": "^4.4.1", "drizzle-orm": "^0.44.2", "ip-bigint": "^8.2.1", "is-cidr": "^5.1.1", "is-ip": "^5.0.1", "js-sha256": "^0.11.1", "nuxt": "^3.17.5", "otpauth": "^9.4.0", "ping": "^0.4.4", "pinia": "^3.0.3", "qr": "^0.5.0", "radix-vue": "^1.9.17", "semver": "^7.7.2", "tailwindcss": "^3.4.17", "timeago.js": "^4.0.2", "vue": "latest", "vue3-apexcharts": "^1.8.0", "zod": "^3.25.67"}, "devDependencies": {"@nuxt/eslint": "^1.4.1", "@types/debug": "^4.1.12", "@types/phc__format": "^1.0.1", "@types/semver": "^7.7.0", "drizzle-kit": "^0.31.4", "esbuild": "^0.25.5", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "tsx": "^4.20.3", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}, "packageManager": "pnpm@10.12.4"}