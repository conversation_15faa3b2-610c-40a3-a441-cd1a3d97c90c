# WireGuard Go 管理系统设计方案

## 项目概述

基于 Go + Gin + MongoDB + Redis 实现的 WireGuard 管理系统，支持用户间网络隔离，可嵌入现有项目。

## 数据模型设计

### MongoDB 集合结构

```go
// 用户 WireGuard 服务器配置
type UserWGServer struct {
    ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
    UserID      primitive.ObjectID `bson:"user_id" json:"user_id" binding:"required"`
    Name        string            `bson:"name" json:"name" binding:"required"`
    Interface   string            `bson:"interface" json:"interface"`     // wg-user1
    Namespace   string            `bson:"namespace" json:"namespace"`     // ns-user1
    IPv4CIDR    string            `bson:"ipv4_cidr" json:"ipv4_cidr"`    // ********/24
    IPv6CIDR    string            `bson:"ipv6_cidr" json:"ipv6_cidr"`    // fd42:42:1::0/64
    Port        int               `bson:"port" json:"port"`               // 51821, 51822...
    PrivateKey  string            `bson:"private_key" json:"-"`           // 加密存储
    PublicKey   string            `bson:"public_key" json:"public_key"`
    Endpoint    string            `bson:"endpoint" json:"endpoint"`       // your-server.com
    DNS         []string          `bson:"dns" json:"dns"`
    MTU         int               `bson:"mtu" json:"mtu"`
    Enabled     bool              `bson:"enabled" json:"enabled"`
    CreatedAt   time.Time         `bson:"created_at" json:"created_at"`
    UpdatedAt   time.Time         `bson:"updated_at" json:"updated_at"`
}

// WireGuard 客户端配置
type WGClient struct {
    ID                  primitive.ObjectID `bson:"_id,omitempty" json:"id"`
    UserID              primitive.ObjectID `bson:"user_id" json:"user_id"`
    ServerID            primitive.ObjectID `bson:"server_id" json:"server_id"`
    Name                string            `bson:"name" json:"name" binding:"required"`
    IPv4Address         string            `bson:"ipv4_address" json:"ipv4_address"`
    IPv6Address         string            `bson:"ipv6_address" json:"ipv6_address"`
    PrivateKey          string            `bson:"private_key" json:"-"`
    PublicKey           string            `bson:"public_key" json:"public_key"`
    PreSharedKey        string            `bson:"pre_shared_key" json:"-"`
    AllowedIPs          []string          `bson:"allowed_ips" json:"allowed_ips"`
    PersistentKeepalive int               `bson:"persistent_keepalive" json:"persistent_keepalive"`
    Enabled             bool              `bson:"enabled" json:"enabled"`
    ExpiresAt           *time.Time        `bson:"expires_at,omitempty" json:"expires_at,omitempty"`
    LastHandshake       *time.Time        `bson:"last_handshake,omitempty" json:"last_handshake,omitempty"`
    TransferRx          int64             `bson:"transfer_rx" json:"transfer_rx"`
    TransferTx          int64             `bson:"transfer_tx" json:"transfer_tx"`
    CreatedAt           time.Time         `bson:"created_at" json:"created_at"`
    UpdatedAt           time.Time         `bson:"updated_at" json:"updated_at"`
}

// 统计信息
type ClientStats struct {
    ClientID      primitive.ObjectID `bson:"client_id" json:"client_id"`
    RxBytes       int64             `bson:"rx_bytes" json:"rx_bytes"`
    TxBytes       int64             `bson:"tx_bytes" json:"tx_bytes"`
    LastHandshake *time.Time        `bson:"last_handshake" json:"last_handshake"`
    IsOnline      bool              `bson:"is_online" json:"is_online"`
    Timestamp     time.Time         `bson:"timestamp" json:"timestamp"`
}
```

## 服务架构设计

### 核心接口定义

```go
// WireGuard 管理器主接口
type WireGuardManager interface {
    // 服务器管理
    CreateUserServer(ctx context.Context, userID primitive.ObjectID, config *CreateServerRequest) (*UserWGServer, error)
    DeleteUserServer(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) error
    GetUserServer(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) (*UserWGServer, error)
    ListUserServers(ctx context.Context, userID primitive.ObjectID) ([]*UserWGServer, error)
    UpdateUserServer(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID, updates *UpdateServerRequest) error
    
    // 客户端管理
    CreateClient(ctx context.Context, userID primitive.ObjectID, req *CreateClientRequest) (*WGClient, error)
    UpdateClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID, req *UpdateClientRequest) error
    DeleteClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) error
    GetClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) (*WGClient, error)
    ListClients(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) ([]*WGClient, error)
    
    // 配置管理
    GenerateClientConfig(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) (string, error)
    GenerateQRCode(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) ([]byte, error)
    ApplyServerConfig(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) error
    
    // 状态管理
    EnableClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) error
    DisableClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) error
    GetClientStats(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) (*ClientStats, error)
}

// 网络命名空间管理器
type NamespaceManager interface {
    CreateNamespace(userID primitive.ObjectID) error
    DeleteNamespace(userID primitive.ObjectID) error
    ExecInNamespace(userID primitive.ObjectID, cmd []string) ([]byte, error)
    SetupWireGuardInterface(userID primitive.ObjectID, config *NetworkConfig) error
    TeardownWireGuardInterface(userID primitive.ObjectID, interfaceName string) error
}

// 配置服务
type ConfigService interface {
    GenerateKeys() (privateKey, publicKey string, err error)
    GeneratePreSharedKey() (string, error)
    AllocateIPAddress(userID primitive.ObjectID, serverID primitive.ObjectID) (ipv4, ipv6 string, err error)
    ReleaseIPAddress(userID primitive.ObjectID, serverID primitive.ObjectID, ipv4, ipv6 string) error
}
```

### 请求/响应结构

```go
// 创建服务器请求
type CreateServerRequest struct {
    Name     string   `json:"name" binding:"required,min=1,max=50"`
    Endpoint string   `json:"endpoint" binding:"required"`
    DNS      []string `json:"dns"`
    MTU      int      `json:"mtu" binding:"min=1280,max=9000"`
}

// 更新服务器请求
type UpdateServerRequest struct {
    Name     *string   `json:"name,omitempty"`
    Endpoint *string   `json:"endpoint,omitempty"`
    DNS      *[]string `json:"dns,omitempty"`
    MTU      *int      `json:"mtu,omitempty"`
    Enabled  *bool     `json:"enabled,omitempty"`
}

// 创建客户端请求
type CreateClientRequest struct {
    ServerID            primitive.ObjectID `json:"server_id" binding:"required"`
    Name                string            `json:"name" binding:"required,min=1,max=50"`
    AllowedIPs          []string          `json:"allowed_ips"`
    PersistentKeepalive int               `json:"persistent_keepalive" binding:"min=0,max=65535"`
    ExpiresAt           *time.Time        `json:"expires_at,omitempty"`
}

// 更新客户端请求
type UpdateClientRequest struct {
    Name                *string    `json:"name,omitempty"`
    AllowedIPs          *[]string  `json:"allowed_ips,omitempty"`
    PersistentKeepalive *int       `json:"persistent_keepalive,omitempty"`
    Enabled             *bool      `json:"enabled,omitempty"`
    ExpiresAt           *time.Time `json:"expires_at,omitempty"`
}

// 网络配置
type NetworkConfig struct {
    Interface  string
    PrivateKey string
    IPv4CIDR   string
    IPv6CIDR   string
    Port       int
}
```

## API 路由设计

### Gin 路由组

```go
// 在现有 Gin 项目中添加 WireGuard 路由
func SetupWireGuardRoutes(r *gin.Engine, wgManager WireGuardManager) {
    wg := r.Group("/api/v1/wireguard")
    wg.Use(AuthMiddleware()) // 使用现有的认证中间件
    wg.Use(WireGuardMiddleware(wgManager))
    
    // 服务器管理
    servers := wg.Group("/servers")
    {
        servers.POST("", createServer)
        servers.GET("", listServers)
        servers.GET("/:id", getServer)
        servers.PUT("/:id", updateServer)
        servers.DELETE("/:id", deleteServer)
        servers.POST("/:id/restart", restartServer)
    }
    
    // 客户端管理
    clients := wg.Group("/clients")
    {
        clients.POST("", createClient)
        clients.GET("", listClients) // 支持 ?server_id= 查询参数
        clients.GET("/:id", getClient)
        clients.PUT("/:id", updateClient)
        clients.DELETE("/:id", deleteClient)
        clients.POST("/:id/enable", enableClient)
        clients.POST("/:id/disable", disableClient)
    }
    
    // 配置和下载
    configs := wg.Group("/configs")
    {
        configs.GET("/clients/:id", getClientConfig)
        configs.GET("/clients/:id/qrcode", getClientQRCode)
    }
    
    // 统计信息
    stats := wg.Group("/stats")
    {
        stats.GET("/clients/:id", getClientStats)
        stats.GET("/servers/:id", getServerStats)
        stats.GET("/overview", getOverviewStats)
    }
}

// 中间件
func WireGuardMiddleware(wgManager WireGuardManager) gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Set("wgManager", wgManager)
        c.Next()
    }
}
```

## 核心实现

### 网络命名空间管理器实现

```go
package wireguard

import (
    "fmt"
    "os/exec"
    "strconv"
    "strings"
    "go.mongodb.org/mongo-driver/bson/primitive"
)

type namespaceManager struct {
    basePort int
}

func NewNamespaceManager(basePort int) NamespaceManager {
    return &namespaceManager{basePort: basePort}
}

func (nm *namespaceManager) CreateNamespace(userID primitive.ObjectID) error {
    namespace := fmt.Sprintf("ns-user-%s", userID.Hex()[:8])

    // 1. 创建网络命名空间
    cmd := exec.Command("ip", "netns", "add", namespace)
    if err := cmd.Run(); err != nil {
        return fmt.Errorf("failed to create namespace %s: %w", namespace, err)
    }

    // 2. 启用 loopback 接口
    cmd = exec.Command("ip", "netns", "exec", namespace, "ip", "link", "set", "lo", "up")
    if err := cmd.Run(); err != nil {
        return fmt.Errorf("failed to enable loopback in namespace %s: %w", namespace, err)
    }

    return nil
}

func (nm *namespaceManager) SetupWireGuardInterface(userID primitive.ObjectID, config *NetworkConfig) error {
    namespace := fmt.Sprintf("ns-user-%s", userID.Hex()[:8])

    // 1. 创建 WireGuard 接口
    cmd := exec.Command("ip", "netns", "exec", namespace, "ip", "link", "add", "dev", config.Interface, "type", "wireguard")
    if err := cmd.Run(); err != nil {
        return fmt.Errorf("failed to create interface: %w", err)
    }

    // 2. 设置私钥
    cmd = exec.Command("ip", "netns", "exec", namespace, "wg", "set", config.Interface, "private-key", "/dev/stdin")
    cmd.Stdin = strings.NewReader(config.PrivateKey)
    if err := cmd.Run(); err != nil {
        return fmt.Errorf("failed to set private key: %w", err)
    }

    // 3. 配置 IP 地址
    cmd = exec.Command("ip", "netns", "exec", namespace, "ip", "addr", "add", config.IPv4CIDR, "dev", config.Interface)
    if err := cmd.Run(); err != nil {
        return fmt.Errorf("failed to set IPv4 address: %w", err)
    }

    if config.IPv6CIDR != "" {
        cmd = exec.Command("ip", "netns", "exec", namespace, "ip", "addr", "add", config.IPv6CIDR, "dev", config.Interface)
        if err := cmd.Run(); err != nil {
            return fmt.Errorf("failed to set IPv6 address: %w", err)
        }
    }

    // 4. 启用接口
    cmd = exec.Command("ip", "netns", "exec", namespace, "ip", "link", "set", config.Interface, "up")
    if err := cmd.Run(); err != nil {
        return fmt.Errorf("failed to bring up interface: %w", err)
    }

    // 5. 设置监听端口
    cmd = exec.Command("ip", "netns", "exec", namespace, "wg", "set", config.Interface, "listen-port", strconv.Itoa(config.Port))
    if err := cmd.Run(); err != nil {
        return fmt.Errorf("failed to set listen port: %w", err)
    }

    // 6. 设置防火墙规则（网络隔离）
    if err := nm.setupFirewallRules(namespace, config); err != nil {
        return fmt.Errorf("failed to setup firewall rules: %w", err)
    }

    return nil
}

func (nm *namespaceManager) setupFirewallRules(namespace string, config *NetworkConfig) error {
    // 允许 WireGuard 流量
    cmd := exec.Command("ip", "netns", "exec", namespace, "iptables", "-A", "INPUT", "-i", config.Interface, "-j", "ACCEPT")
    if err := cmd.Run(); err != nil {
        return err
    }

    cmd = exec.Command("ip", "netns", "exec", namespace, "iptables", "-A", "OUTPUT", "-o", config.Interface, "-j", "ACCEPT")
    if err := cmd.Run(); err != nil {
        return err
    }

    // 启用 NAT
    cmd = exec.Command("ip", "netns", "exec", namespace, "iptables", "-t", "nat", "-A", "POSTROUTING", "-s", config.IPv4CIDR, "-j", "MASQUERADE")
    return cmd.Run()
}

func (nm *namespaceManager) DeleteNamespace(userID primitive.ObjectID) error {
    namespace := fmt.Sprintf("ns-user-%s", userID.Hex()[:8])
    cmd := exec.Command("ip", "netns", "del", namespace)
    return cmd.Run()
}

func (nm *namespaceManager) ExecInNamespace(userID primitive.ObjectID, cmd []string) ([]byte, error) {
    namespace := fmt.Sprintf("ns-user-%s", userID.Hex()[:8])
    args := append([]string{"netns", "exec", namespace}, cmd...)
    execCmd := exec.Command("ip", args...)
    return execCmd.Output()
}
```

### 配置服务实现

```go
package wireguard

import (
    "crypto/rand"
    "encoding/base64"
    "fmt"
    "net"
    "context"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/bson/primitive"
    "go.mongodb.org/mongo-driver/mongo"
    "golang.zx2c4.com/wireguard/wgctrl/wgtypes"
)

type configService struct {
    db     *mongo.Database
    config *Config
}

func NewConfigService(config *Config) ConfigService {
    return &configService{config: config}
}

func (cs *configService) GenerateKeys() (privateKey, publicKey string, err error) {
    key, err := wgtypes.GeneratePrivateKey()
    if err != nil {
        return "", "", err
    }

    privateKey = key.String()
    publicKey = key.PublicKey().String()
    return privateKey, publicKey, nil
}

func (cs *configService) GeneratePreSharedKey() (string, error) {
    key, err := wgtypes.GenerateKey()
    if err != nil {
        return "", err
    }
    return key.String(), nil
}

func (cs *configService) AllocateIPAddress(userID primitive.ObjectID, serverID primitive.ObjectID) (ipv4, ipv6 string, err error) {
    // 获取服务器信息
    var server UserWGServer
    err = cs.db.Collection("wg_servers").FindOne(context.Background(), bson.M{
        "_id": serverID,
        "user_id": userID,
    }).Decode(&server)
    if err != nil {
        return "", "", err
    }

    // 解析 IPv4 CIDR
    _, ipv4Net, err := net.ParseCIDR(server.IPv4CIDR)
    if err != nil {
        return "", "", err
    }

    // 获取已分配的 IP 地址
    cursor, err := cs.db.Collection("wg_clients").Find(context.Background(), bson.M{
        "server_id": serverID,
    })
    if err != nil {
        return "", "", err
    }
    defer cursor.Close(context.Background())

    usedIPs := make(map[string]bool)
    for cursor.Next(context.Background()) {
        var client WGClient
        if err := cursor.Decode(&client); err != nil {
            continue
        }
        usedIPs[client.IPv4Address] = true
    }

    // 分配新的 IP 地址
    ip := ipv4Net.IP
    for ip := ip.Mask(ipv4Net.Mask); ipv4Net.Contains(ip); inc(ip) {
        ipStr := ip.String()
        // 跳过网络地址、广播地址和网关地址
        if ip.Equal(ipv4Net.IP) || ip[3] == 1 || ip[3] == 255 {
            continue
        }
        if !usedIPs[ipStr] {
            ipv4 = ipStr
            break
        }
    }

    if ipv4 == "" {
        return "", "", fmt.Errorf("no available IPv4 addresses")
    }

    // 生成对应的 IPv6 地址
    if server.IPv6CIDR != "" {
        _, ipv6Net, err := net.ParseCIDR(server.IPv6CIDR)
        if err == nil {
            // 使用 IPv4 的最后一个字节作为 IPv6 的最后一个字节
            ipv6Addr := make(net.IP, 16)
            copy(ipv6Addr, ipv6Net.IP)
            ipv6Addr[15] = ip[3]
            ipv6 = ipv6Addr.String()
        }
    }

    return ipv4, ipv6, nil
}

func inc(ip net.IP) {
    for j := len(ip) - 1; j >= 0; j-- {
        ip[j]++
        if ip[j] > 0 {
            break
        }
    }
}

func (cs *configService) ReleaseIPAddress(userID primitive.ObjectID, serverID primitive.ObjectID, ipv4, ipv6 string) error {
    // IP 地址会在客户端删除时自动释放，这里可以添加额外的清理逻辑
    return nil
}
```

### Redis 缓存服务

```go
package wireguard

import (
    "context"
    "fmt"
    "time"
    "encoding/json"
    "github.com/go-redis/redis/v8"
    "go.mongodb.org/mongo-driver/bson/primitive"
)

type CacheService struct {
    redis *redis.Client
}

func NewCacheService(redis *redis.Client) *CacheService {
    return &CacheService{redis: redis}
}

func (cs *CacheService) CacheClientConfig(userID, clientID primitive.ObjectID, config string) error {
    key := fmt.Sprintf("wg:config:%s:%s", userID.Hex(), clientID.Hex())
    return cs.redis.Set(context.Background(), key, config, 24*time.Hour).Err()
}

func (cs *CacheService) GetCachedClientConfig(userID, clientID primitive.ObjectID) (string, error) {
    key := fmt.Sprintf("wg:config:%s:%s", userID.Hex(), clientID.Hex())
    return cs.redis.Get(context.Background(), key).Result()
}

func (cs *CacheService) CacheClientStats(userID, clientID primitive.ObjectID, stats *ClientStats) error {
    key := fmt.Sprintf("wg:stats:%s:%s", userID.Hex(), clientID.Hex())
    data, err := json.Marshal(stats)
    if err != nil {
        return err
    }
    return cs.redis.Set(context.Background(), key, data, 5*time.Minute).Err()
}

func (cs *CacheService) GetCachedClientStats(userID, clientID primitive.ObjectID) (*ClientStats, error) {
    key := fmt.Sprintf("wg:stats:%s:%s", userID.Hex(), clientID.Hex())
    data, err := cs.redis.Get(context.Background(), key).Result()
    if err != nil {
        return nil, err
    }

    var stats ClientStats
    err = json.Unmarshal([]byte(data), &stats)
    return &stats, err
}

func (cs *CacheService) InvalidateUserCache(userID primitive.ObjectID) error {
    pattern := fmt.Sprintf("wg:*:%s:*", userID.Hex())
    keys, err := cs.redis.Keys(context.Background(), pattern).Result()
    if err != nil {
        return err
    }
    if len(keys) > 0 {
        return cs.redis.Del(context.Background(), keys...).Err()
    }
    return nil
}

func (cs *CacheService) SetUserOnline(userID primitive.ObjectID, clientID primitive.ObjectID) error {
    key := fmt.Sprintf("wg:online:%s:%s", userID.Hex(), clientID.Hex())
    return cs.redis.Set(context.Background(), key, time.Now().Unix(), 10*time.Minute).Err()
}

func (cs *CacheService) IsUserOnline(userID primitive.ObjectID, clientID primitive.ObjectID) bool {
    key := fmt.Sprintf("wg:online:%s:%s", userID.Hex(), clientID.Hex())
    _, err := cs.redis.Get(context.Background(), key).Result()
    return err == nil
}
```

### API 处理器实现

```go
package handlers

import (
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
    "go.mongodb.org/mongo-driver/bson/primitive"
)

// 创建服务器
func createServer(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    var req CreateServerRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    server, err := wgManager.CreateUserServer(c.Request.Context(), userID, &req)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusCreated, server)
}

// 获取服务器列表
func listServers(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    servers, err := wgManager.ListUserServers(c.Request.Context(), userID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, servers)
}

// 创建客户端
func createClient(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    var req CreateClientRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    client, err := wgManager.CreateClient(c.Request.Context(), userID, &req)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusCreated, client)
}

// 获取客户端配置
func getClientConfig(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
        return
    }

    config, err := wgManager.GenerateClientConfig(c.Request.Context(), userID, clientID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.Header("Content-Type", "text/plain")
    c.Header("Content-Disposition", "attachment; filename=client.conf")
    c.String(http.StatusOK, config)
}

// 获取客户端 QR 码
func getClientQRCode(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
        return
    }

    qrCode, err := wgManager.GenerateQRCode(c.Request.Context(), userID, clientID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.Header("Content-Type", "image/png")
    c.Data(http.StatusOK, "image/png", qrCode)
}

// 启用客户端
func enableClient(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
        return
    }

    if err := wgManager.EnableClient(c.Request.Context(), userID, clientID); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "Client enabled successfully"})
}

// 禁用客户端
func disableClient(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
        return
    }

    if err := wgManager.DisableClient(c.Request.Context(), userID, clientID); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "Client disabled successfully"})
}

// 获取客户端统计
func getClientStats(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
        return
    }

    stats, err := wgManager.GetClientStats(c.Request.Context(), userID, clientID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, stats)
}

// 从上下文获取用户ID的辅助函数
func getUserIDFromContext(c *gin.Context) primitive.ObjectID {
    userIDStr, exists := c.Get("user_id")
    if !exists {
        panic("user_id not found in context")
    }

    userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
    if err != nil {
        panic("invalid user_id format")
    }

    return userID
}
```

## 配置和部署

### 配置文件结构

```yaml
# config.yaml
mongodb:
  uri: "mongodb://localhost:27017"
  database: "wireguard_db"

redis:
  addr: "localhost:6379"
  password: ""
  db: 0

wireguard:
  base_port: 51820
  max_users: 1000
  max_servers_per_user: 5
  max_clients_per_server: 100
  base_network: "********/16"
  default_dns: ["*******", "*******"]
  default_mtu: 1420

security:
  encryption_key: "your-32-byte-encryption-key-here"

server:
  host: "0.0.0.0"
  port: 8080
  mode: "release" # debug, release
```

### 初始化代码

```go
package main

import (
    "context"
    "log"
    "net/http"
    "time"

    "github.com/gin-gonic/gin"
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
    "github.com/go-redis/redis/v8"
    "gopkg.in/yaml.v2"
    "io/ioutil"
)

type Config struct {
    MongoDB struct {
        URI      string `yaml:"uri"`
        Database string `yaml:"database"`
    } `yaml:"mongodb"`

    Redis struct {
        Addr     string `yaml:"addr"`
        Password string `yaml:"password"`
        DB       int    `yaml:"db"`
    } `yaml:"redis"`

    WireGuard struct {
        BasePort            int      `yaml:"base_port"`
        MaxUsers            int      `yaml:"max_users"`
        MaxServersPerUser   int      `yaml:"max_servers_per_user"`
        MaxClientsPerServer int      `yaml:"max_clients_per_server"`
        BaseNetwork         string   `yaml:"base_network"`
        DefaultDNS          []string `yaml:"default_dns"`
        DefaultMTU          int      `yaml:"default_mtu"`
    } `yaml:"wireguard"`

    Security struct {
        EncryptionKey string `yaml:"encryption_key"`
    } `yaml:"security"`

    Server struct {
        Host string `yaml:"host"`
        Port int    `yaml:"port"`
        Mode string `yaml:"mode"`
    } `yaml:"server"`
}

func main() {
    // 1. 加载配置
    config, err := loadConfig("config.yaml")
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }

    // 2. 连接 MongoDB
    mongoClient, err := mongo.Connect(context.Background(), options.Client().ApplyURI(config.MongoDB.URI))
    if err != nil {
        log.Fatal("Failed to connect to MongoDB:", err)
    }
    defer mongoClient.Disconnect(context.Background())

    // 3. 连接 Redis
    redisClient := redis.NewClient(&redis.Options{
        Addr:     config.Redis.Addr,
        Password: config.Redis.Password,
        DB:       config.Redis.DB,
    })
    defer redisClient.Close()

    // 4. 初始化 WireGuard 服务
    db := mongoClient.Database(config.MongoDB.Database)
    wgManager := NewWireGuardManager(db, redisClient, config)

    // 5. 设置 Gin 路由
    gin.SetMode(config.Server.Mode)
    r := gin.Default()

    // 添加现有的认证中间件
    r.Use(AuthMiddleware())

    // 设置 WireGuard 路由
    SetupWireGuardRoutes(r, wgManager)

    // 6. 启动服务器
    addr := fmt.Sprintf("%s:%d", config.Server.Host, config.Server.Port)
    log.Printf("Starting server on %s", addr)

    server := &http.Server{
        Addr:         addr,
        Handler:      r,
        ReadTimeout:  30 * time.Second,
        WriteTimeout: 30 * time.Second,
    }

    log.Fatal(server.ListenAndServe())
}

func loadConfig(filename string) (*Config, error) {
    data, err := ioutil.ReadFile(filename)
    if err != nil {
        return nil, err
    }

    var config Config
    err = yaml.Unmarshal(data, &config)
    return &config, err
}

// 示例认证中间件（需要根据你的现有项目调整）
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 从 JWT token 或 session 中获取用户ID
        // 这里需要根据你现有的认证系统进行调整

        token := c.GetHeader("Authorization")
        if token == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
            c.Abort()
            return
        }

        // 验证 token 并获取用户ID
        userID, err := validateTokenAndGetUserID(token)
        if err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
            c.Abort()
            return
        }

        c.Set("user_id", userID)
        c.Next()
    }
}

func validateTokenAndGetUserID(token string) (string, error) {
    // 实现你的 token 验证逻辑
    // 返回用户ID
    return "user_id_from_token", nil
}
```

## 使用示例

### API 调用示例

```bash
# 1. 创建 WireGuard 服务器
curl -X POST http://localhost:8080/api/v1/wireguard/servers \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My VPN Server",
    "endpoint": "vpn.example.com",
    "dns": ["*******", "*******"],
    "mtu": 1420
  }'

# 2. 创建客户端
curl -X POST http://localhost:8080/api/v1/wireguard/clients \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": "60f1b2c3d4e5f6789abcdef0",
    "name": "My Phone",
    "allowed_ips": ["0.0.0.0/0"],
    "persistent_keepalive": 25
  }'

# 3. 下载客户端配置
curl -X GET http://localhost:8080/api/v1/wireguard/configs/clients/60f1b2c3d4e5f6789abcdef1 \
  -H "Authorization: Bearer your-jwt-token" \
  -o client.conf

# 4. 获取 QR 码
curl -X GET http://localhost:8080/api/v1/wireguard/configs/clients/60f1b2c3d4e5f6789abcdef1/qrcode \
  -H "Authorization: Bearer your-jwt-token" \
  -o qrcode.png

# 5. 启用/禁用客户端
curl -X POST http://localhost:8080/api/v1/wireguard/clients/60f1b2c3d4e5f6789abcdef1/enable \
  -H "Authorization: Bearer your-jwt-token"

curl -X POST http://localhost:8080/api/v1/wireguard/clients/60f1b2c3d4e5f6789abcdef1/disable \
  -H "Authorization: Bearer your-jwt-token"
```

## 安全考虑

1. **权限隔离**: 用户只能访问自己的 WireGuard 资源
2. **密钥加密**: 私钥和预共享密钥使用 AES 加密存储
3. **网络隔离**: 通过命名空间和 iptables 规则确保用户间流量隔离
4. **资源限制**: 限制每个用户的服务器和客户端数量
5. **输入验证**: 所有 API 输入都经过严格验证
6. **审计日志**: 记录所有重要操作的日志

## 监控和维护

1. **统计收集**: 定期收集 WireGuard 统计信息
2. **健康检查**: 监控服务器和客户端状态
3. **自动清理**: 清理过期的客户端和配置
4. **备份策略**: 定期备份数据库和配置文件
5. **性能监控**: 监控系统资源使用情况

这个设计方案提供了完整的 WireGuard 管理功能，支持用户间网络隔离，可以很好地集成到你现有的 Gin + MongoDB + Redis 项目中。
