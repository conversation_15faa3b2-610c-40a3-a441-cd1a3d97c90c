name: Production

on:
  workflow_dispatch:
  push:
    tags:
      - "v*"

# This workflow does not support fixing old versions
# as this will break the latest and major tags

jobs:
  docker-build:
    name: Build Docker
    runs-on: ${{ matrix.arch.os }}
    if: |
      github.repository_owner == 'wg-easy' &&
      startsWith(github.ref, 'refs/tags/v')
    permissions:
      packages: write
    strategy:
      fail-fast: false
      matrix:
        arch:
          - platform: linux/amd64
            os: ubuntu-latest
          - platform: linux/arm64
            os: ubuntu-24.04-arm
          - platform: linux/arm/v7
            os: ubuntu-24.04-arm
    steps:
      - uses: actions/checkout@v4

      - name: Prepare
        run: |
          platform=${{ matrix.arch.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ghcr.io/wg-easy/wg-easy
          flavor: |
            latest=false

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push by digest
        id: build
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: ${{ matrix.arch.platform }}
          labels: ${{ steps.meta.outputs.labels }}
          tags: ghcr.io/wg-easy/wg-easy
          outputs: type=image,push-by-digest=true,name-canonical=true,push=true
          cache-from: type=gha,scope=build-${{ env.PLATFORM_PAIR }}
          cache-to: type=gha,mode=min,scope=build-${{ env.PLATFORM_PAIR }}

      - name: Export digest
        run: |
          mkdir -p ${{ runner.temp }}/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "${{ runner.temp }}/digests/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: ${{ runner.temp }}/digests/*
          if-no-files-found: error
          retention-days: 1

  docker-merge:
    name: Merge & Deploy Docker
    runs-on: ubuntu-latest
    if: |
      github.repository_owner == 'wg-easy' &&
      startsWith(github.ref, 'refs/tags/v')
    permissions:
      packages: write
    needs: docker-build
    steps:
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: ${{ runner.temp }}/digests
          pattern: digests-*
          merge-multiple: true

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ghcr.io/wg-easy/wg-easy
          flavor: |
            latest=false
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Create manifest list and push
        working-directory: ${{ runner.temp }}/digests
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf 'ghcr.io/wg-easy/wg-easy@sha256:%s ' *)

      - name: Inspect image
        run: |
          docker buildx imagetools inspect ghcr.io/wg-easy/wg-easy:${{ steps.meta.outputs.version }}

  docs:
    name: Build & Deploy Docs
    runs-on: ubuntu-latest
    if: |
      github.repository_owner == 'wg-easy' &&
      startsWith(github.ref, 'refs/tags/v')
    permissions:
      contents: write
    needs: docker-merge
    steps:
      - uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.9
          cache: "pip"
          cache-dependency-path: docs/requirements.txt

      - name: Install Dependencies
        run: |
          pip install -r docs/requirements.txt

      - name: Setup Git User
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'

      - name: Build Docs Website
        run: |
          cd docs
          git fetch origin gh-pages --depth=1 || true

          # Extract version numbers
          DOCS_VERSION=${GITHUB_REF#refs/tags/} # e.g. v1.2.3 or v1.2.3-beta
          MINOR_VERSION=$(echo $DOCS_VERSION | cut -d. -f1,2) # e.g. v1.2

          # Check if it's a stable release (only numbers, no '-')
          if [[ "$DOCS_VERSION" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            echo "Stable release detected: $DOCS_VERSION"
            mike deploy --push --update-aliases $MINOR_VERSION latest
          else
            echo "Pre-release detected: $DOCS_VERSION"
            mike deploy --push --update-aliases Pre-release
          fi
