name: Lint

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  docs:
    name: Check Docs
    runs-on: ubuntu-latest
    if: github.repository_owner == 'wg-easy'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "lts/*"
          check-latest: true
          cache: "pnpm"

      - name: Check docs formatting
        run: |
          pnpm install
          pnpm format:check:docs

  lint:
    name: Lint
    runs-on: ubuntu-latest
    needs: docs
    if: github.repository_owner == 'wg-easy'

    strategy:
      fail-fast: false
      max-parallel: 3
      matrix:
        command: ["lint", "typecheck", "format:check"]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "lts/*"
          check-latest: true
          cache: "pnpm"

      - name: pnpm ${{ matrix.command }}
        run: |
          cd src
          pnpm install
          pnpm ${{ matrix.command }}
