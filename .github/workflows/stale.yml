# This workflow warns and then closes issues and PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: Mark stale issues and pull requests

on:
  workflow_dispatch:
  schedule:
    - cron: "*/5 * * * *"

jobs:
  stale:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'wg-easy'
    permissions:
      issues: write
      pull-requests: write

    steps:
      - uses: actions/stale@v9
        with:
          days-before-issue-stale: 30
          days-before-issue-close: 14
          stale-issue-label: "stale"
          stale-issue-message: "This issue is stale because it has been open for 30 days with no activity."
          close-issue-message: "This issue was closed because it has been inactive for 14 days since being marked as stale."
          days-before-pr-stale: 30
          days-before-pr-close: 14
          stale-pr-message: "This PR is stale because it has been open for 30 days with no activity."
          close-pr-message: "This PR was closed because it has been inactive for 14 days since being marked as stale."
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          operations-per-run: 100
