name: Pull Request

on:
  workflow_dispatch:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  docker:
    name: Build Docker
    runs-on: ${{ matrix.arch.os }}
    if: github.repository_owner == 'wg-easy'
    strategy:
      fail-fast: false
      matrix:
        arch:
          - platform: linux/amd64
            os: ubuntu-latest
          - platform: linux/arm64
            os: ubuntu-24.04-arm
          - platform: linux/arm/v7
            os: ubuntu-24.04-arm
    steps:
      - uses: actions/checkout@v4

      - name: Prepare
        run: |
          platform=${{ matrix.arch.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Docker Image
        uses: docker/build-push-action@v6
        with:
          context: .
          push: false
          platforms: ${{ matrix.arch.platform }}
          tags: ghcr.io/wg-easy/wg-easy:pr
          cache-from: type=gha
          cache-to: type=gha,mode=min,scope=build-${{ env.PLATFORM_PAIR }}
